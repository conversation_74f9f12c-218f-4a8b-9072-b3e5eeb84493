#include "headfile.h"

uint8_t K1,K1_last;
uint8_t K2,K2_last;

uint8_t model_switch;

void key_scan()
{
    K1 =   DL_GPIO_readPins(GPIO_Key_K1_PORT,GPIO_Key_K1_PIN);
    K2 =   DL_GPIO_readPins(GPIO_Key_K2_PORT,GPIO_Key_K2_PIN);

    if(K1 = 0 && K1_last == 1)
    {
       model_switch++;

    }
     if(K2 = 0 && K2_last == 1)
    {
        model_switch--;

    }
   K1_last = K1;
   K2_last = K2;

}