#ifndef __OLED_H
#define __OLED_H
#include "ti_msp_dl_config.h"
#include <stdint.h>
#include "OLED_DATA.h"
#ifndef GPIO_OLED_PIN_SCL_PORT
#define GPIO_OLED_PIN_SCL_PORT GPIO_OLED_PORT 
#endif

#ifndef GPIO_OLED_PIN_SDA_PORT
#define GPIO_OLED_PIN_SDA_PORT GPIO_OLED_PORT 
#endif
/*参数宏定义*********************/

/*FontSize参数取值*/
/*此参数值不仅用于判断，而且用于计算横向字符偏移，默认值为字体像素宽度*/
#define OLED_8X16				8
#define OLED_7X12				7
#define OLED_6X8				6

/*汉字大小参数取值*/
#define OLED_12X12				12
#define OLED_16X16				16

/*IsFilled参数数值*/
#define OLED_UNFILLED			0
#define OLED_FILLED				1

/*********************参数宏定义*/
//有关帧率显示的结构体
typedef struct FPSCounter{
	uint16_t Value; 
	uint16_t Count;
} FPSCounter;	//帧率相关结构体并直接实例化

/*函数声明*********************/

/*初始化函数*/
void OLED_Init(void);

/*亮度控制函数*/
void OLED_Brightness(int16_t Brightness);

/*更新函数*/
void OLED_Update(void);
void OLED_UpdateArea(uint8_t X, uint8_t Y, uint8_t Width, uint8_t Height);

/*显存控制函数*/
void OLED_Clear(void);
void OLED_ClearArea(uint8_t X, uint8_t Y, uint8_t Width, uint8_t Height);
void OLED_Reverse(void);
void OLED_ReverseArea(int16_t X, int16_t Y, uint8_t Width, uint8_t Height);

/*显示函数*/
void OLED_ShowChar(uint8_t X, uint8_t Y, char Char, uint8_t FontSize);
void OLED_ShowCharArea(uint8_t RangeX,uint8_t RangeY,uint8_t RangeWidth,uint8_t RangeHeight, int16_t X, int16_t Y, char Char, uint8_t FontSize);
void OLED_ShowString(uint8_t X, uint8_t Y, char *String, uint8_t FontSize);
void OLED_ShowMixString(uint8_t X, uint8_t Y, char *String, uint8_t ChineseFontSize,uint8_t ASCIIFontSize);
void OLED_ShowMixStringArea(uint8_t RangeX,uint8_t RangeY,uint8_t RangeWidth,uint8_t RangeHeight,int16_t X, int16_t Y, char *String, uint8_t ChineseFontSize,uint8_t ASCIIFontSize);
void OLED_ShowStringArea(uint8_t RangeX,uint8_t RangeY,uint8_t RangeWidth,uint8_t RangeHeight, int16_t X, int16_t Y, char *String, uint8_t FontSize);
void OLED_ShowNum(uint8_t X, uint8_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowSignedNum(uint8_t X, uint8_t Y, int32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowHexNum(uint8_t X, uint8_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowBinNum(uint8_t X, uint8_t Y, uint32_t Number, uint8_t Length, uint8_t FontSize);
void OLED_ShowFloatNum(uint8_t X, uint8_t Y, double Number, uint8_t IntLength, uint8_t FraLength, uint8_t FontSize);
void OLED_ShowChinese(uint8_t X, uint8_t Y, char *Chinese, uint8_t size);
void OLED_ShowChineseArea(uint8_t RangeX,uint8_t RangeY,uint8_t RangeWidth,uint8_t RangeHeight, int16_t X, int16_t Y, char *Chinese, uint8_t FontSize);
void OLED_ShowImage(int16_t X, int16_t Y, uint16_t Width, uint16_t Height, const uint8_t *Image);
void OLED_ShowImageArea(int16_t X1, int16_t Y1, int16_t PictureWidth, int16_t PictureHeight, int16_t X2, int16_t Y2, int16_t AreaWidth, int16_t AreaHeight, const uint8_t *Image);
void OLED_Printf(uint8_t X, uint8_t Y, uint8_t FontSize, char *format, ...);
void OLED_PrintfMix(int16_t X, int16_t Y, uint8_t ChineseFontSize,uint8_t ASCIIFontSize, char *format, ...);
void OLED_PrintfMixArea(uint8_t RangeX,uint8_t RangeY,uint8_t RangeWidth,uint8_t RangeHeight,int16_t X, uint16_t Y, uint8_t ChineseFontSize,uint8_t ASCIIFontSize, char *format, ...);
void OLED_PrintfArea(uint8_t RangeX,uint8_t RangeY,uint8_t RangeWidth,uint8_t RangeHeight, int16_t X, int16_t Y,uint8_t FontSize, char *format, ...);
/*绘图函数*/
void OLED_DrawPoint(uint8_t X, uint8_t Y);
uint8_t OLED_GetPoint(uint8_t X, uint8_t Y);
void OLED_DrawLine(uint8_t X0, uint8_t Y0, uint8_t X1, uint8_t Y1);
void OLED_DrawRectangle(uint8_t X, uint8_t Y, uint8_t Width, uint8_t Height, uint8_t IsFilled);
void OLED_DrawTriangle(uint8_t X0, uint8_t Y0, uint8_t X1, uint8_t Y1, uint8_t X2, uint8_t Y2, uint8_t IsFilled);
void OLED_DrawCircle(uint8_t X, uint8_t Y, uint8_t Radius, uint8_t IsFilled);
void OLED_DrawEllipse(uint8_t X, uint8_t Y, uint8_t A, uint8_t B, uint8_t IsFilled);
void OLED_DrawArc(uint8_t X, uint8_t Y, uint8_t Radius, int16_t StartAngle, int16_t EndAngle, uint8_t IsFilled);

/*********************函数声明*/

#endif


/*****************江协科技|版权所有****************/
/*****************jiangxiekeji.com*****************/
