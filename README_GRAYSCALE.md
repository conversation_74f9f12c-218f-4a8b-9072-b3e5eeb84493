# 灰度传感器工程

## 功能说明
本工程已简化为仅保留灰度传感器功能，实现8路灰度传感器的数据采集和0/1数字输出。

## 主要功能
- 8路灰度传感器数据采集
- 自动黑白校准和阈值处理
- 数字化输出(0表示白色，1表示黑色)
- 实时数据处理

## 核心文件
- `main.c` - 主程序文件，包含传感器初始化和数据处理
- `track.c/track.h` - 灰度传感器驱动库
- `ADC.c/ADC.h` - ADC采集功能

## 使用方法

### 1. 初始化
```c
No_MCU_Sensor sensor;
unsigned short white[8] = {2085,2888,1872,2056,1726,1054,2099,1551}; // 白色校准值
unsigned short black[8] = {377,659,271,243,88,89,146,100}; // 黑色校准值

No_MCU_Ganv_Sensor_Init(&sensor, white, black);
```

### 2. 数据采集
```c
// 执行传感器数据采集
No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);

// 获取8位数字输出
digital_output = Get_Digtal_For_User(&sensor);

// 处理输出数据
Output_Digital_Values(digital_output);
```

### 3. 获取传感器值
```c
// 获取单个传感器值(0-7)
unsigned char value = Get_Sensor_Value(sensor_index);

// 获取所有传感器原始值
unsigned char all_values = Get_All_Sensors_Raw();
```

## 输出格式
- 每个传感器输出0或1
- 0 = 检测到白色
- 1 = 检测到黑色
- 8个传感器从左到右编号为0-7

## 校准说明
- white[8]: 白色校准值数组，需要在白色表面采集
- black[8]: 黑色校准值数组，需要在黑色表面采集
- 系统会自动计算灰度阈值进行二值化处理

## 硬件连接
- 传感器通过3位地址线(A0,A1,A2)选择通道
- ADC输入连接到传感器模拟输出
- 地址线连接到GPIO控制引脚

## 修改内容总结
1. **移除了所有非灰度传感器相关功能**：
   - 删除了电机控制相关代码
   - 删除了OLED显示功能
   - 删除了JY61P陀螺仪相关代码
   - 删除了按键扫描功能
   - 删除了PID控制和路径跟踪算法

2. **简化了main.c文件**：
   - 仅保留灰度传感器初始化和数据采集
   - 添加了0/1数字输出功能
   - 提供了便于使用的接口函数

3. **新增功能**：
   - `Get_Sensor_Value(index)` - 获取单个传感器的0/1值
   - `Get_All_Sensors_Raw()` - 获取8位原始数字值
   - `Output_Digital_Values()` - 处理和存储数字输出

## 使用示例
参考 `example_usage.c` 文件查看具体的使用方法，包括：
- 黑线位置检测
- 路径模式识别
- PID误差计算

## 注意事项
1. 使用前需要正确校准黑白值
2. 确保ADC配置正确(当前配置为12位)
3. 传感器方向可通过Direction宏调整
4. 采样频率可通过延时调整
5. 编译时可能出现头文件路径警告，这是IDE配置问题，不影响实际功能
