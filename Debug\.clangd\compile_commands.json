[{"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/ADC.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug/ti_msp_dl_config.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Delay.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/EMM.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED/OLED.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED/OLED_DATA.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P/JY61P.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/key.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/main.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/motor.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/track.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/uart_my.c"}, {"directory": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug", "command": "clang++ -D__MSPM0G3507__ -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED\" -I\"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include\" -I\"C:/ti/mspm0_sdk_2_01_00_03/source\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/armv7em-ti-none-eabihf/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c++/v1\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/lib/clang/15.0.7/include\" -isystem\"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/include/c\" -xc", "file": "C:/Users/<USER>/workspace_ccstheia/new_24H copy/example_usage.c"}]