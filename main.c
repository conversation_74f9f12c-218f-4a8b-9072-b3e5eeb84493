/*
    程序功能：读取jy系列传感器欧拉角数据并将欧拉角显示在OLED上，上电时将偏航角置零，按下开发板上的按键S2时手动偏航角置零。
    须   知：需要在上位机将传感器串口设置为19200或将本程序的串口波特率修改为传感器的波特率。
            其次，最好在上位机上将输出数据的除了欧拉角的部分全部取消勾选，只保留欧拉角。
    程序作者：B站 @上nm网课呢
    OLED部分作者：B站 @江协科技 移植与修改：B站 @上nm网课呢
    MSPM0G3507教程：B站 @Torris-Yin , 感谢这位老师发布的教程

*/

#include "ti_msp_dl_config.h"
#include "headfile.h"


extern float Pitch,Roll,Yaw;
extern FPSCounter FPS;

 uint32_t Count1 = 0;

/*定义变量*/
float Target, Actuala,Actualb;			//目标值，实际值，输出值
float Kp, Ki, Kd;					//比例项，积分项，微分项的权重
float Error0a, Error1a, ErrorInta,Error0b, Error1b, ErrorIntb;		//本次误差，上次误差，误差积分
int Outa,Outb;
uint8_t T;
uint16_t car_state;
float E,E_finally,E0,E1,EI;
int result;

uint8_t no_track,no_track_last;
uint8_t angle_flag;
uint8_t track_num;

/*寻迹参数*/
float Target_err, Actual_err;			//目标值，实际值
float Kp_err = 0, Ki_err, Kd_err = 0;					//比例项，积分项，微分项的权重
float Error0_err, Error1_err, ErrorInt_err;		//本次误差，上次误差，误差积分
float Out_err;

/*用于任务三修正轨迹*/
uint16_t Count3;	
uint16_t Count4;	

uint16_t Count3_flag;
uint16_t Count4_flag;

/*检测多少次走斜线*/
uint8_t confrim_track_flag;

unsigned short Anolog[8]={0};
unsigned short white[8]={2085,2888,1872,2056,1726,1054,2099,1551};
unsigned short black[8]={377,659,271,243,88,89,146,100};
unsigned short Normal[8];
unsigned char rx_buff[256]={0};

float Err;
unsigned char Digtal;
float xun,last_xun;
/*Err代码*/
float Track_Err() ;

char test[20];

int main(void)
{
    No_MCU_Sensor sensor;
	 NVIC_EnableIRQ(TIMER_0_INST_INT_IRQN);      // 配置定时器的NVIC
    DL_TimerG_startCounter(TIMER_0_INST);       // 初始化定时器
    NVIC_EnableIRQ(TIMER_control_INST_INT_IRQN);      // 配置定时器的NVIC
    DL_TimerG_startCounter(TIMER_control_INST);       // 初始化定时器
    SYSCFG_DL_init();
    No_MCU_Ganv_Sensor_Init(&sensor,white,black);
    motor_init();
    OLED_Init();
    OLED_Clear();
  
    while(1)
    {
        key_scan();
        //无时基传感器常规任务，包含模拟量，数字量，归一化量
			No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);
			//有时基传感器常规任务，包含模拟量，数字量，归一化量
//			No_Mcu_Ganv_Sensor_Task_With_tick(&sensor)
			//获取传感器数字量结果(只有当有黑白值传入进去了之后才会有这个值！！)
			Digtal=Get_Digtal_For_User(&sensor);
        //  set_pwm(-20,20);
      Err =  Track_Err();
      OLED_Printf(0, 0, OLED_6X8, "model:%d  ", K1);
      OLED_Printf(0, 10, OLED_6X8, "%.2f  ", Error0_err);
      OLED_Update();
      //sprintf(test, "%.2f   ",Err);
      //send_char(UART0,Err);
    
    }

}
float Track_Err() 
{
		float z = 0;
		uint8_t l4 = 0,l3 = 0,l2 = 0,l1 = 0,r1 = 0,r2 = 0,r3 = 0,r4 = 0;
		
		if((Digtal & 0x80) == 0) {l4 = 1;z++;} else{l4 = 0;}
		if((Digtal & 0x40) == 0) {l3 = 1;z++;} else{l3 = 0;}
		if((Digtal & 0x20) == 0) {l2 = 1;z++;} else{l2 = 0;}
		if((Digtal & 0x10) == 0) {l1 = 1;z++;} else{l1 = 0;}
		if((Digtal & 0x08) == 0) {r1 = 1;z++;} else{r1 = 0;}
		if((Digtal & 0x04) == 0) {r2 = 1;z++;} else{r2 = 0;}
		if((Digtal & 0x02) == 0) {r3 = 1;z++;} else{r3 = 0;}
		if((Digtal & 0x01) == 0) {r4 = 1;z++;} else{r4 = 0;}
		
		//if(Digtal == 0xFF) {xun = 100;}	// 走白地
		xun = (l4*1 + l3*2 + l2*3 + l1*4 + r1*5 + r2*6 + r3*7 + r4*8)/z;
		if(xun != 0 && (xun < 101 && xun > 99)) {last_xun = xun;}
		else if(xun == 0) {xun = last_xun;}
       
//    // 状态修正逻辑保持不变
//    if (car_state == 0x1012 || car_state == 0x1013) 
//			{
//        if (Err <= 0) Err = 0;  // 仅允许正误差（右转弯）
//    } else if (car_state == 0x1022 || car_state == 0x1023) 
//			{
//        if (Err >= 0) Err = 0;  // 仅允许负误差（左转弯）
//    }
    
    return xun;
}

//定时器中断函数
void TIMER_0_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_0_INST)) {
        case DL_TIMER_IIDX_ZERO:
            /************************************定时器中断，每10ms进入一次********************************************/
            //Count1++;
            
            if(FPS.Count<100){FPS.Count++;}else{FPS.Count=0;OLED_Printf(92,55,OLED_6X8,"FPS:%2d",FPS.Value);FPS.Value=0;}   //在右上角显示帧率
           

            /************************************定时器中断，每10ms进入一次********************************************/
            break;


        default:
            break;
    }
}

/*主控函数，1ms进一次中断*/
void TIMG6_IRQHandler(void)     
{
    switch (DL_TimerG_getPendingInterrupt(TIMER_control_INST)) {
        case DL_TIMER_IIDX_ZERO:
        Count1++;
        if(Count1 >= 40)
		{   
            Count1 = 0;

            Target_err = 4.5;
            Actual_err = Err;

            Error1_err = Error0_err;
            Error0_err = Target_err - Actual_err;

            ErrorInt_err += Error0_err;

            result =  5 * Error0_err + 0 *ErrorInt_err  + 10 *(Error0_err - Error1_err);
            
            set_pwm(-20+result,20+result);
            
		}
        
        break;


        default:
            break;
    }

}








