/*
    程序功能：灰度传感器检测，输出0,1数字信号
    程序作者：修改自原工程
*/

#include "ti_msp_dl_config.h"
#include "track.h"
#include "ADC.h"

// 函数声明
void Output_Digital_Values(unsigned char digital_data);
unsigned char Get_Sensor_Value(unsigned char sensor_index);
unsigned char Get_All_Sensors_Raw(void);

// 灰度传感器相关变量
No_MCU_Sensor sensor; // 传感器结构体
unsigned short white[8]={2085,2888,1872,2056,1726,1054,2099,1551}; // 白色校准值
unsigned short black[8]={377,659,271,243,88,89,146,100}; // 黑色校准值
unsigned char digital_output; // 数字输出结果
unsigned char sensor_values[8]; // 存储8个传感器的0/1值

int main(void)
{
    SYSCFG_DL_init(); // 系统初始化
    No_MCU_Ganv_Sensor_Init(&sensor,white,black); // 初始化灰度传感器

    while(1)
    {
        // 执行传感器数据采集和处理
        No_Mcu_Ganv_Sensor_Task_Without_tick(&sensor);

        // 获取数字输出结果(8位，每位代表一个传感器的0/1状态)
        digital_output = Get_Digtal_For_User(&sensor);

        // 输出每个传感器的0/1状态到串口或其他接口
        Output_Digital_Values(digital_output);

        // 简单延时
        DL_Common_delayCycles(100000); // 约10ms延时
    }
}
// 输出数字值函数 - 将8位数字量分解为8个0/1值输出
void Output_Digital_Values(unsigned char digital_data)
{
    for(int i = 0; i < 8; i++)
    {
        // 检查第i位是否为1，存储到数组中
        if(digital_data & (1 << i))
        {
            sensor_values[i] = 1; // 传感器i检测到黑线
        }
        else
        {
            sensor_values[i] = 0; // 传感器i检测到白色
        }
    }

    // 可以在这里添加其他输出方式，如：
    // - 通过串口发送数据
    // - 控制GPIO输出
    // - 存储到全局变量供其他函数使用
}

// 获取指定传感器的0/1值
unsigned char Get_Sensor_Value(unsigned char sensor_index)
{
    if(sensor_index < 8)
        return sensor_values[sensor_index];
    else
        return 0; // 无效索引返回0
}

// 获取所有传感器的原始数字值
unsigned char Get_All_Sensors_Raw(void)
{
    return digital_output;
}