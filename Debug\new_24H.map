******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 14:28:32 2025

OUTPUT FILE NAME:   <new_24H.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003c59


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005270  0001ad90  R  X
  SRAM                  20200000   00008000  000007d2  0000782e  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005270   00005270    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004240   00004240    r-x .text
  00004300    00004300    00000f20   00000f20    r-- .rodata
  00005220    00005220    00000050   00000050    r-- .cinit
20200000    20200000    000005d2   00000000    rw-
  20200000    20200000    000005a4   00000000    rw- .bss
  202005a4    202005a4    0000002e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004240     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002b8     OLED.o (.text.OLED_ShowImage)
                  00000d48    000002b4     JY61P.o (.text.UART2_IRQHandler)
                  00000ffc    00000230     main.o (.text.Track_Err)
                  0000122c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000144c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001628    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000017ba    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000017bc    00000188     track.o (.text.No_MCU_Ganv_Sensor_Init)
                  00001944    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001a80    00000120            : _printfi.c.obj (.text._pconv_e)
                  00001ba0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001cac    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00001db0    000000ec     main.o (.text.TIMG6_IRQHandler)
                  00001e9c    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001f84    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002068    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002144    000000dc     OLED.o (.text.OLED_ClearArea)
                  00002220    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000022fc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000023d4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000024ac    000000d0     track.o (.text.Get_Analog_value)
                  0000257c    000000c0     main.o (.text.main)
                  0000263c    000000c0     motor.o (.text.set_pwm)
                  000026fc    000000b0     OLED.o (.text.OLED_ShowChar)
                  000027ac    000000aa     track.o (.text.normalizeAnalogValues)
                  00002856    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002858    000000a2                            : udivmoddi4.S.obj (.text)
                  000028fa    00000002     --HOLE-- [fill = 0]
                  000028fc    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000299c    0000009a     OLED.o (.text.OLED_Init)
                  00002a36    00000002     --HOLE-- [fill = 0]
                  00002a38    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWMA_init)
                  00002ac4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002b50    00000088     key.o (.text.key_scan)
                  00002bd8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002c5a    00000002     --HOLE-- [fill = 0]
                  00002c5c    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002cd8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002d4c    00000072     track.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002dbe    0000006c     track.o (.text.convertAnalogToDigital)
                  00002e2a    00000002     --HOLE-- [fill = 0]
                  00002e2c    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002e94    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002efa    00000002     --HOLE-- [fill = 0]
                  00002efc    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002f5e    00000002     --HOLE-- [fill = 0]
                  00002f60    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00002fc2    00000002     --HOLE-- [fill = 0]
                  00002fc4    0000005c     main.o (.text.TIMG0_IRQHandler)
                  00003020    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000307c    00000058     OLED.o (.text.OLED_Clear)
                  000030d4    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000312c    00000058            : _printfi.c.obj (.text._pconv_f)
                  00003184    00000056     OLED.o (.text.OLED_ShowString)
                  000031da    00000002     --HOLE-- [fill = 0]
                  000031dc    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003232    00000054     OLED.o (.text.OLED_I2C_SendByte)
                  00003286    00000002     --HOLE-- [fill = 0]
                  00003288    00000054     OLED.o (.text.OLED_Update)
                  000032dc    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000332e    00000050     OLED.o (.text.OLED_WriteData)
                  0000337e    00000002     --HOLE-- [fill = 0]
                  00003380    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000033cc    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_JY61P_init)
                  00003418    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00003462    0000004a     OLED.o (.text.OLED_GPIO_Init)
                  000034ac    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000034f6    00000002     --HOLE-- [fill = 0]
                  000034f8    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00003540    00000048     ADC.o (.text.adc_getValue)
                  00003588    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  000035cc    00000042     track.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000360e    00000002     --HOLE-- [fill = 0]
                  00003610    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00003650    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  00003690    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000036d0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003710    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00003750    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00003790    00000040     libc.a : atoi.c.obj (.text.atoi)
                  000037d0    0000003e     OLED.o (.text.OLED_Printf)
                  0000380e    00000002     --HOLE-- [fill = 0]
                  00003810    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  0000384c    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00003888    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000038c4    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003900    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000393c    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00003976    00000002     --HOLE-- [fill = 0]
                  00003978    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000039b2    00000002     --HOLE-- [fill = 0]
                  000039b4    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000039ec    00000036     OLED.o (.text.OLED_SetCursor)
                  00003a22    00000002     --HOLE-- [fill = 0]
                  00003a24    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003a58    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003a8c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_control_init)
                  00003ac0    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00003af0    00000030     OLED.o (.text.OLED_W_SCL)
                  00003b20    00000030     OLED.o (.text.OLED_W_SDA)
                  00003b50    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003b80    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00003bac    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003bd8    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003c04    0000002a     OLED.o (.text.OLED_WriteCommand)
                  00003c2e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003c56    00000002     --HOLE-- [fill = 0]
                  00003c58    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003c80    00000024     OLED.o (.text.OLED_I2C_Start)
                  00003ca4    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003cc8    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003cec    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003d0e    00000002     --HOLE-- [fill = 0]
                  00003d10    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003d30    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003d50    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003d6e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003d8c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003daa    00000002     --HOLE-- [fill = 0]
                  00003dac    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00003dc8    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00003de4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003e00    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003e1c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003e38    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003e54    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003e70    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003e8c    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003ea8    0000001c     OLED.o (.text.OLED_I2C_Stop)
                  00003ec4    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003edc    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003ef4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003f0c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003f24    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003f3c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003f54    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00003f6c    00000018     motor.o (.text.DL_GPIO_setPins)
                  00003f84    00000018     track.o (.text.DL_GPIO_setPins)
                  00003f9c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003fb4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003fcc    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00003fe4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00003ffc    00000018     main.o (.text.DL_Timer_startCounter)
                  00004014    00000018     motor.o (.text.DL_Timer_startCounter)
                  0000402c    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004044    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  0000405c    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004074    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  0000408a    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  000040a0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000040b6    00000016     key.o (.text.DL_GPIO_readPins)
                  000040cc    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000040e2    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000040f6    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000410a    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000411e    00000014     track.o (.text.DL_GPIO_clearPins)
                  00004132    00000002     --HOLE-- [fill = 0]
                  00004134    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00004148    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  0000415c    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004170    00000014     JY61P.o (.text.DL_UART_receiveData)
                  00004184    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00004198    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000041ac    00000012     main.o (.text.DL_Timer_getPendingInterrupt)
                  000041be    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000041d0    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  000041e2    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000041f4    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00004204    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004214    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004224    00000010     motor.o (.text.motor_init)
                  00004234    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004244    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00004254    0000000e     track.o (.text.Get_Digtal_For_User)
                  00004262    00000002     --HOLE-- [fill = 0]
                  00004264    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004272    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00004280    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  0000428e    00000002     --HOLE-- [fill = 0]
                  00004290    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000429c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000042a6    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000042b0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000042c0    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000042ca    0000000a            : vsprintf.c.obj (.text._outc)
                  000042d4    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000042dc    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000042e4    00000006     libc.a : exit.c.obj (.text:abort)
                  000042ea    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000042ee    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000042f2    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000042f6    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000042fa    00000006     --HOLE-- [fill = 0]

.cinit     0    00005220    00000050     
                  00005220    0000002b     (.cinit..data.load) [load image, compression = lzss]
                  0000524b    00000001     --HOLE-- [fill = 0]
                  0000524c    0000000c     (__TI_handler_table)
                  00005258    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005260    00000010     (__TI_cinit_table)

.rodata    0    00004300    00000f20     
                  00004300    000005f0     OLED_DATA.o (.rodata.OLED_F8x16)
                  000048f0    00000532     OLED_DATA.o (.rodata.OLED_F7x12)
                  00004e22    0000023a     OLED_DATA.o (.rodata.OLED_F6x8)
                  0000505c    00000003     ti_msp_dl_config.o (.rodata.gPWMAClockConfig)
                  0000505f    00000001     --HOLE-- [fill = 0]
                  00005060    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005161    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005164    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  0000518c    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000051a0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_controlTimerConfig)
                  000051b4    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  000051c5    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  000051d6    0000000b     main.o (.rodata.str1.15159059442110792349.1)
                  000051e1    00000001     --HOLE-- [fill = 0]
                  000051e2    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000051ec    0000000a     ti_msp_dl_config.o (.rodata.gUART_JY61PConfig)
                  000051f6    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000051f8    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005200    00000008     ti_msp_dl_config.o (.rodata.gPWMAConfig)
                  00005208    00000008     main.o (.rodata.str1.17100691992556644108.1)
                  00005210    00000007     main.o (.rodata.str1.8154729771448623357.1)
                  00005217    00000003     ti_msp_dl_config.o (.rodata.gTIMER_controlClockConfig)
                  0000521a    00000002     ti_msp_dl_config.o (.rodata.gUART_JY61PClockConfig)
                  0000521c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000005a4     UNINITIALIZED
                  20200000    00000400     (.common:OLED_DisplayBuf)
                  20200400    000000bc     (.common:gPWMABackup)
                  202004bc    000000a0     (.common:gTIMER_controlBackup)
                  2020055c    00000009     (.common:receivedData)
                  20200565    00000001     (.common:Digtal)
                  20200566    00000001     (.common:K1)
                  20200567    00000001     (.common:K1_last)
                  20200568    00000004     (.common:Actual_err)
                  2020056c    00000004     (.common:Err)
                  20200570    00000004     (.common:Error0_err)
                  20200574    00000004     (.common:Error1_err)
                  20200578    00000004     (.common:ErrorInt_err)
                  2020057c    00000004     (.common:Pitch)
                  20200580    00000004     (.common:Roll)
                  20200584    00000004     (.common:Target_err)
                  20200588    00000004     (.common:Yaw)
                  2020058c    00000004     (.common:last_xun)
                  20200590    00000004     (.common:result)
                  20200594    00000004     (.common:xun)
                  20200598    00000001     (.common:K2)
                  20200599    00000001     (.common:K2_last)
                  2020059a    00000001     (.common:PitchH)
                  2020059b    00000001     (.common:PitchL)
                  2020059c    00000001     (.common:RollH)
                  2020059d    00000001     (.common:RollL)
                  2020059e    00000001     (.common:SUM)
                  2020059f    00000001     (.common:VH)
                  202005a0    00000001     (.common:VL)
                  202005a1    00000001     (.common:YawH)
                  202005a2    00000001     (.common:YawL)
                  202005a3    00000001     (.common:model_switch)

.data      0    202005a4    0000002e     UNINITIALIZED
                  202005a4    00000010     main.o (.data.black)
                  202005b4    00000010     main.o (.data.white)
                  202005c4    00000004     main.o (.data.Count1)
                  202005c8    00000004     OLED.o (.data.FPS)
                  202005cc    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202005d0    00000001     JY61P.o (.data.RxState)
                  202005d1    00000001     JY61P.o (.data.dataIndex)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             1922    129       348    
       main.o                         1166    26        73     
       track.o                        1116    0         0      
       motor.o                        276     0         0      
       ADC.o                          236     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       key.o                          158     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         4882    347       426    
                                                               
    .\OLED\
       OLED_DATA.o                    0       3420      0      
       OLED.o                         2104    0         1028   
    +--+------------------------------+-------+---------+---------+
       Total:                         2104    3420      1028   
                                                               
    .\USART_JY61P\
       JY61P.o                        712     0         32     
    +--+------------------------------+-------+---------+---------+
       Total:                         712     0         32     
                                                               
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      90      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1040    0         0      
                                                               
    D:\apps\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5406    291       4      
                                                               
    D:\apps\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\apps\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2762    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       79        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   16910   4137      2002   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005260 records: 2, size/record: 8, table size: 16
	.data: load addr=00005220, load size=0000002b bytes, run addr=202005a4, run size=0000002e bytes, compression=lzss
	.bss: load addr=00005258, load size=00000008 bytes, run addr=20200000, run size=000005a4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000524c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001629     000042b0     000042ae   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000042eb  ADC0_IRQHandler                      
000042eb  ADC1_IRQHandler                      
000042eb  AES_IRQHandler                       
20200568  Actual_err                           
000042ee  C$$EXIT                              
000042eb  CANFD0_IRQHandler                    
202005c4  Count1                               
000042eb  DAC0_IRQHandler                      
00003611  DL_ADC12_setClockConfig              
0000429d  DL_Common_delayCycles                
00002069  DL_SYSCTL_configSYSPLL               
00003589  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00001cad  DL_Timer_initFourCCPWMMode           
00001e9d  DL_Timer_initTimerMode               
00003e55  DL_Timer_setCaptCompUpdateMethod     
00003fe5  DL_Timer_setCaptureCompareOutCtl     
00004215  DL_Timer_setCaptureCompareValue      
00003e71  DL_Timer_setClockConfig              
000034f9  DL_UART_init                         
000041bf  DL_UART_setClockConfig               
000042eb  DMA_IRQHandler                       
000042eb  Default_Handler                      
20200565  Digtal                               
2020056c  Err                                  
20200570  Error0_err                           
20200574  Error1_err                           
20200578  ErrorInt_err                         
202005c8  FPS                                  
000042eb  GROUP0_IRQHandler                    
000042eb  GROUP1_IRQHandler                    
000024ad  Get_Analog_value                     
00004255  Get_Digtal_For_User                  
000042ef  HOSTexit                             
000042eb  HardFault_Handler                    
000042eb  I2C0_IRQHandler                      
000042eb  I2C1_IRQHandler                      
20200566  K1                                   
20200567  K1_last                              
20200598  K2                                   
20200599  K2_last                              
000042eb  NMI_Handler                          
000017bd  No_MCU_Ganv_Sensor_Init              
00002d4d  No_MCU_Ganv_Sensor_Init_Frist        
000035cd  No_Mcu_Ganv_Sensor_Task_Without_tick 
0000307d  OLED_Clear                           
00002145  OLED_ClearArea                       
20200000  OLED_DisplayBuf                      
00004e22  OLED_F6x8                            
000048f0  OLED_F7x12                           
00004300  OLED_F8x16                           
00003463  OLED_GPIO_Init                       
00003233  OLED_I2C_SendByte                    
00003c81  OLED_I2C_Start                       
00003ea9  OLED_I2C_Stop                        
0000299d  OLED_Init                            
000037d1  OLED_Printf                          
000039ed  OLED_SetCursor                       
000026fd  OLED_ShowChar                        
00000a91  OLED_ShowImage                       
00003185  OLED_ShowString                      
00003289  OLED_Update                          
00003af1  OLED_W_SCL                           
00003b21  OLED_W_SDA                           
00003c05  OLED_WriteCommand                    
0000332f  OLED_WriteData                       
000042eb  PendSV_Handler                       
2020057c  Pitch                                
2020059a  PitchH                               
2020059b  PitchL                               
000042eb  RTC_IRQHandler                       
000042f3  Reset_Handler                        
20200580  Roll                                 
2020059c  RollH                                
2020059d  RollL                                
202005d0  RxState                              
000042eb  SPI0_IRQHandler                      
000042eb  SPI1_IRQHandler                      
2020059e  SUM                                  
000042eb  SVC_Handler                          
00003651  SYSCFG_DL_ADC1_init                  
00002221  SYSCFG_DL_GPIO_init                  
00002a39  SYSCFG_DL_PWMA_init                  
0000384d  SYSCFG_DL_SYSCTL_init                
00003a59  SYSCFG_DL_TIMER_0_init               
00003a8d  SYSCFG_DL_TIMER_control_init         
00003691  SYSCFG_DL_UART_0_init                
000033cd  SYSCFG_DL_UART_JY61P_init            
000036d1  SYSCFG_DL_init                       
000028fd  SYSCFG_DL_initPower                  
000042eb  SysTick_Handler                      
000042eb  TIMA0_IRQHandler                     
000042eb  TIMA1_IRQHandler                     
00002fc5  TIMG0_IRQHandler                     
000042eb  TIMG12_IRQHandler                    
00001db1  TIMG6_IRQHandler                     
000042eb  TIMG7_IRQHandler                     
000042eb  TIMG8_IRQHandler                     
000041d1  TI_memcpy_small                      
00004281  TI_memset_small                      
20200584  Target_err                           
00000ffd  Track_Err                            
000042eb  UART0_IRQHandler                     
000042eb  UART1_IRQHandler                     
00000d49  UART2_IRQHandler                     
000042eb  UART3_IRQHandler                     
2020059f  VH                                   
202005a0  VL                                   
20200588  Yaw                                  
202005a1  YawH                                 
202005a2  YawL                                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005260  __TI_CINIT_Base                      
00005270  __TI_CINIT_Limit                     
00005270  __TI_CINIT_Warm                      
0000524c  __TI_Handler_Table_Base              
00005258  __TI_Handler_Table_Limit             
00003901  __TI_auto_init_nobinit_nopinit       
00002c5d  __TI_decompress_lzss                 
000041e3  __TI_decompress_none                 
000030d5  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004245  __TI_zero_init                       
00001633  __adddf3                             
000023df  __addsf3                             
00005060  __aeabi_ctype_table_                 
00005060  __aeabi_ctype_table_C                
000034ad  __aeabi_d2iz                         
00001633  __aeabi_dadd                         
00002efd  __aeabi_dcmpeq                       
00002f39  __aeabi_dcmpge                       
00002f4d  __aeabi_dcmpgt                       
00002f25  __aeabi_dcmple                       
00002f11  __aeabi_dcmplt                       
00001ba1  __aeabi_ddiv                         
00001f85  __aeabi_dmul                         
00001629  __aeabi_dsub                         
202005cc  __aeabi_errno                        
000042d5  __aeabi_errno_addr                   
00003751  __aeabi_f2d                          
000039b5  __aeabi_f2iz                         
000023df  __aeabi_fadd                         
00002f61  __aeabi_fcmpeq                       
00002f9d  __aeabi_fcmpge                       
00002fb1  __aeabi_fcmpgt                       
00002f89  __aeabi_fcmple                       
00002f75  __aeabi_fcmplt                       
00002bd9  __aeabi_fdiv                         
00002ac5  __aeabi_fmul                         
000023d5  __aeabi_fsub                         
00003bad  __aeabi_i2d                          
00003889  __aeabi_i2f                          
000031dd  __aeabi_idiv                         
000017bb  __aeabi_idiv0                        
000031dd  __aeabi_idivmod                      
00002857  __aeabi_ldiv0                        
00003d8d  __aeabi_llsl                         
00003cc9  __aeabi_lmul                         
00004291  __aeabi_memclr                       
00004291  __aeabi_memclr4                      
00004291  __aeabi_memclr8                      
000042dd  __aeabi_memcpy                       
000042dd  __aeabi_memcpy4                      
000042dd  __aeabi_memcpy8                      
00004265  __aeabi_memset                       
00004265  __aeabi_memset4                      
00004265  __aeabi_memset8                      
00003ca5  __aeabi_ui2d                         
00003711  __aeabi_uidiv                        
00003711  __aeabi_uidivmod                     
00004185  __aeabi_uldivmod                     
00003d8d  __ashldi3                            
ffffffff  __binit__                            
00002e2d  __cmpdf2                             
0000393d  __cmpsf2                             
00001ba1  __divdf3                             
00002bd9  __divsf3                             
00002e2d  __eqdf2                              
0000393d  __eqsf2                              
00003751  __extendsfdf2                        
000034ad  __fixdfsi                            
000039b5  __fixsfsi                            
00003bad  __floatsidf                          
00003889  __floatsisf                          
00003ca5  __floatunsidf                        
00002cd9  __gedf2                              
000038c5  __gesf2                              
00002cd9  __gtdf2                              
000038c5  __gtsf2                              
00002e2d  __ledf2                              
0000393d  __lesf2                              
00002e2d  __ltdf2                              
0000393d  __ltsf2                              
UNDEFED   __mpu_init                           
00001f85  __muldf3                             
00003cc9  __muldi3                             
00003979  __muldsi3                            
00002ac5  __mulsf3                             
00002e2d  __nedf2                              
0000393d  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001629  __subdf3                             
000023d5  __subsf3                             
00002859  __udivmoddi4                         
00003c59  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000042f7  _system_pre_init                     
000042e5  abort                                
00003541  adc_getValue                         
00003791  atoi                                 
ffffffff  binit                                
202005a4  black                                
00002dbf  convertAnalogToDigital               
202005d1  dataIndex                            
00003021  frexp                                
00003021  frexpl                               
20200400  gPWMABackup                          
202004bc  gTIMER_controlBackup                 
00000000  interruptVectors                     
00002b51  key_scan                             
2020058c  last_xun                             
000022fd  ldexp                                
000022fd  ldexpl                               
0000257d  main                                 
00003ced  memccpy                              
202005a3  model_switch                         
00004225  motor_init                           
000027ad  normalizeAnalogValues                
2020055c  receivedData                         
20200590  result                               
000022fd  scalbn                               
000022fd  scalbnl                              
0000263d  set_pwm                              
00003bd9  vsprintf                             
00004235  wcslen                               
202005b4  white                                
20200594  xun                                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  OLED_ShowImage                       
00000d49  UART2_IRQHandler                     
00000ffd  Track_Err                            
00001629  __aeabi_dsub                         
00001629  __subdf3                             
00001633  __adddf3                             
00001633  __aeabi_dadd                         
000017bb  __aeabi_idiv0                        
000017bd  No_MCU_Ganv_Sensor_Init              
00001ba1  __aeabi_ddiv                         
00001ba1  __divdf3                             
00001cad  DL_Timer_initFourCCPWMMode           
00001db1  TIMG6_IRQHandler                     
00001e9d  DL_Timer_initTimerMode               
00001f85  __aeabi_dmul                         
00001f85  __muldf3                             
00002069  DL_SYSCTL_configSYSPLL               
00002145  OLED_ClearArea                       
00002221  SYSCFG_DL_GPIO_init                  
000022fd  ldexp                                
000022fd  ldexpl                               
000022fd  scalbn                               
000022fd  scalbnl                              
000023d5  __aeabi_fsub                         
000023d5  __subsf3                             
000023df  __addsf3                             
000023df  __aeabi_fadd                         
000024ad  Get_Analog_value                     
0000257d  main                                 
0000263d  set_pwm                              
000026fd  OLED_ShowChar                        
000027ad  normalizeAnalogValues                
00002857  __aeabi_ldiv0                        
00002859  __udivmoddi4                         
000028fd  SYSCFG_DL_initPower                  
0000299d  OLED_Init                            
00002a39  SYSCFG_DL_PWMA_init                  
00002ac5  __aeabi_fmul                         
00002ac5  __mulsf3                             
00002b51  key_scan                             
00002bd9  __aeabi_fdiv                         
00002bd9  __divsf3                             
00002c5d  __TI_decompress_lzss                 
00002cd9  __gedf2                              
00002cd9  __gtdf2                              
00002d4d  No_MCU_Ganv_Sensor_Init_Frist        
00002dbf  convertAnalogToDigital               
00002e2d  __cmpdf2                             
00002e2d  __eqdf2                              
00002e2d  __ledf2                              
00002e2d  __ltdf2                              
00002e2d  __nedf2                              
00002efd  __aeabi_dcmpeq                       
00002f11  __aeabi_dcmplt                       
00002f25  __aeabi_dcmple                       
00002f39  __aeabi_dcmpge                       
00002f4d  __aeabi_dcmpgt                       
00002f61  __aeabi_fcmpeq                       
00002f75  __aeabi_fcmplt                       
00002f89  __aeabi_fcmple                       
00002f9d  __aeabi_fcmpge                       
00002fb1  __aeabi_fcmpgt                       
00002fc5  TIMG0_IRQHandler                     
00003021  frexp                                
00003021  frexpl                               
0000307d  OLED_Clear                           
000030d5  __TI_ltoa                            
00003185  OLED_ShowString                      
000031dd  __aeabi_idiv                         
000031dd  __aeabi_idivmod                      
00003233  OLED_I2C_SendByte                    
00003289  OLED_Update                          
0000332f  OLED_WriteData                       
000033cd  SYSCFG_DL_UART_JY61P_init            
00003463  OLED_GPIO_Init                       
000034ad  __aeabi_d2iz                         
000034ad  __fixdfsi                            
000034f9  DL_UART_init                         
00003541  adc_getValue                         
00003589  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000035cd  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003611  DL_ADC12_setClockConfig              
00003651  SYSCFG_DL_ADC1_init                  
00003691  SYSCFG_DL_UART_0_init                
000036d1  SYSCFG_DL_init                       
00003711  __aeabi_uidiv                        
00003711  __aeabi_uidivmod                     
00003751  __aeabi_f2d                          
00003751  __extendsfdf2                        
00003791  atoi                                 
000037d1  OLED_Printf                          
0000384d  SYSCFG_DL_SYSCTL_init                
00003889  __aeabi_i2f                          
00003889  __floatsisf                          
000038c5  __gesf2                              
000038c5  __gtsf2                              
00003901  __TI_auto_init_nobinit_nopinit       
0000393d  __cmpsf2                             
0000393d  __eqsf2                              
0000393d  __lesf2                              
0000393d  __ltsf2                              
0000393d  __nesf2                              
00003979  __muldsi3                            
000039b5  __aeabi_f2iz                         
000039b5  __fixsfsi                            
000039ed  OLED_SetCursor                       
00003a59  SYSCFG_DL_TIMER_0_init               
00003a8d  SYSCFG_DL_TIMER_control_init         
00003af1  OLED_W_SCL                           
00003b21  OLED_W_SDA                           
00003bad  __aeabi_i2d                          
00003bad  __floatsidf                          
00003bd9  vsprintf                             
00003c05  OLED_WriteCommand                    
00003c59  _c_int00_noargs                      
00003c81  OLED_I2C_Start                       
00003ca5  __aeabi_ui2d                         
00003ca5  __floatunsidf                        
00003cc9  __aeabi_lmul                         
00003cc9  __muldi3                             
00003ced  memccpy                              
00003d8d  __aeabi_llsl                         
00003d8d  __ashldi3                            
00003e55  DL_Timer_setCaptCompUpdateMethod     
00003e71  DL_Timer_setClockConfig              
00003ea9  OLED_I2C_Stop                        
00003fe5  DL_Timer_setCaptureCompareOutCtl     
00004185  __aeabi_uldivmod                     
000041bf  DL_UART_setClockConfig               
000041d1  TI_memcpy_small                      
000041e3  __TI_decompress_none                 
00004215  DL_Timer_setCaptureCompareValue      
00004225  motor_init                           
00004235  wcslen                               
00004245  __TI_zero_init                       
00004255  Get_Digtal_For_User                  
00004265  __aeabi_memset                       
00004265  __aeabi_memset4                      
00004265  __aeabi_memset8                      
00004281  TI_memset_small                      
00004291  __aeabi_memclr                       
00004291  __aeabi_memclr4                      
00004291  __aeabi_memclr8                      
0000429d  DL_Common_delayCycles                
000042d5  __aeabi_errno_addr                   
000042dd  __aeabi_memcpy                       
000042dd  __aeabi_memcpy4                      
000042dd  __aeabi_memcpy8                      
000042e5  abort                                
000042eb  ADC0_IRQHandler                      
000042eb  ADC1_IRQHandler                      
000042eb  AES_IRQHandler                       
000042eb  CANFD0_IRQHandler                    
000042eb  DAC0_IRQHandler                      
000042eb  DMA_IRQHandler                       
000042eb  Default_Handler                      
000042eb  GROUP0_IRQHandler                    
000042eb  GROUP1_IRQHandler                    
000042eb  HardFault_Handler                    
000042eb  I2C0_IRQHandler                      
000042eb  I2C1_IRQHandler                      
000042eb  NMI_Handler                          
000042eb  PendSV_Handler                       
000042eb  RTC_IRQHandler                       
000042eb  SPI0_IRQHandler                      
000042eb  SPI1_IRQHandler                      
000042eb  SVC_Handler                          
000042eb  SysTick_Handler                      
000042eb  TIMA0_IRQHandler                     
000042eb  TIMA1_IRQHandler                     
000042eb  TIMG12_IRQHandler                    
000042eb  TIMG7_IRQHandler                     
000042eb  TIMG8_IRQHandler                     
000042eb  UART0_IRQHandler                     
000042eb  UART1_IRQHandler                     
000042eb  UART3_IRQHandler                     
000042ee  C$$EXIT                              
000042ef  HOSTexit                             
000042f3  Reset_Handler                        
000042f7  _system_pre_init                     
00004300  OLED_F8x16                           
000048f0  OLED_F7x12                           
00004e22  OLED_F6x8                            
00005060  __aeabi_ctype_table_                 
00005060  __aeabi_ctype_table_C                
0000524c  __TI_Handler_Table_Base              
00005258  __TI_Handler_Table_Limit             
00005260  __TI_CINIT_Base                      
00005270  __TI_CINIT_Limit                     
00005270  __TI_CINIT_Warm                      
20200000  OLED_DisplayBuf                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200400  gPWMABackup                          
202004bc  gTIMER_controlBackup                 
2020055c  receivedData                         
20200565  Digtal                               
20200566  K1                                   
20200567  K1_last                              
20200568  Actual_err                           
2020056c  Err                                  
20200570  Error0_err                           
20200574  Error1_err                           
20200578  ErrorInt_err                         
2020057c  Pitch                                
20200580  Roll                                 
20200584  Target_err                           
20200588  Yaw                                  
2020058c  last_xun                             
20200590  result                               
20200594  xun                                  
20200598  K2                                   
20200599  K2_last                              
2020059a  PitchH                               
2020059b  PitchL                               
2020059c  RollH                                
2020059d  RollL                                
2020059e  SUM                                  
2020059f  VH                                   
202005a0  VL                                   
202005a1  YawH                                 
202005a2  YawL                                 
202005a3  model_switch                         
202005a4  black                                
202005b4  white                                
202005c4  Count1                               
202005c8  FPS                                  
202005cc  __aeabi_errno                        
202005d0  RxState                              
202005d1  dataIndex                            
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[254 symbols]
