/*
    灰度传感器使用示例
    展示如何获取和使用传感器的0/1输出值
*/

#include "ti_msp_dl_config.h"
#include "track.h"
#include "ADC.h"

// 外部函数声明
extern unsigned char Get_Sensor_Value(unsigned char sensor_index);
extern unsigned char Get_All_Sensors_Raw(void);

// 示例：检测黑线位置
void Detect_Line_Position(void)
{
    unsigned char line_detected = 0;
    unsigned char line_position = 0;
    unsigned char sensor_count = 0;
    
    // 检查每个传感器
    for(int i = 0; i < 8; i++)
    {
        if(Get_Sensor_Value(i) == 1) // 检测到黑线
        {
            line_detected = 1;
            line_position += i;
            sensor_count++;
        }
    }
    
    if(line_detected && sensor_count > 0)
    {
        line_position = line_position / sensor_count; // 计算平均位置
        // 在这里可以根据line_position进行控制决策
    }
}

// 示例：检测特殊路径模式
void Detect_Path_Pattern(void)
{
    unsigned char raw_data = Get_All_Sensors_Raw();
    
    // 检测不同的路径模式
    switch(raw_data)
    {
        case 0x00: // 全白 - 无线
            // 处理无线情况
            break;
            
        case 0xFF: // 全黑 - 可能是停止线或起始线
            // 处理特殊标记
            break;
            
        case 0x18: // 中间两个传感器检测到线 (00011000)
            // 直线行驶
            break;
            
        case 0x1C: // 右偏 (00011100)
        case 0x0E: // 右偏 (00001110)
            // 向左调整
            break;
            
        case 0x38: // 左偏 (00111000)
        case 0x70: // 左偏 (01110000)
            // 向右调整
            break;
            
        default:
            // 其他情况的处理
            break;
    }
}

// 示例：简单的PID控制计算
float Calculate_PID_Error(void)
{
    float error = 0.0;
    float weighted_sum = 0.0;
    float sensor_sum = 0.0;
    
    // 加权计算误差
    for(int i = 0; i < 8; i++)
    {
        if(Get_Sensor_Value(i) == 1)
        {
            weighted_sum += (i - 3.5) * 1000; // 中心位置为3.5
            sensor_sum += 1000;
        }
    }
    
    if(sensor_sum > 0)
    {
        error = weighted_sum / sensor_sum;
    }
    
    return error;
}
