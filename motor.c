#include "headfile.h"

void motor_init()
{
    DL_Timer_startCounter(PWMA_INST);
}

void set_pwm(int PWM_L,int PWM_R)
{
    if(PWM_L > 0)
    {
        DL_GPIO_setPins(TB6612_PORT, TB6612_AIN1_PIN);
        DL_GPIO_clearPins(TB6612_PORT, TB6612_AIN2_PIN);
        DL_Timer_setCaptureCompareValue(PWMA_INST, abs(PWM_L),GPIO_PWMA_C0_IDX);
    }
    else 
    {
        DL_GPIO_setPins(TB6612_PORT, TB6612_AIN2_PIN);
        DL_GPIO_clearPins(TB6612_PORT, TB6612_AIN1_PIN);
        DL_Timer_setCaptureCompareValue(PWMA_INST, abs(PWM_L),GPIO_PWMA_C0_IDX);
    }
    if(PWM_R > 0)
    {
        DL_GPIO_setPins(TB6612_PORT, TB6612_BIN2_PIN);
        DL_GPIO_clearPins(TB6612_PORT, TB6612_BIN1_PIN);
        DL_Timer_setCaptureCompareValue(PWMA_INST, abs(PWM_R),GPIO_PWMA_C1_IDX);
    }
    else 
    {
        DL_GPIO_setPins(TB6612_PORT, TB6612_BIN1_PIN);
        DL_GPIO_clearPins(TB6612_PORT, TB6612_BIN2_PIN);
        DL_Timer_setCaptureCompareValue(PWMA_INST, abs(PWM_R),GPIO_PWMA_C1_IDX);
    }


}