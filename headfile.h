#ifndef __headfile_H__
#define __headfile_H__


#include "ti_msp_dl_config.h"

#include "stdio.h"
#include "stdint.h"
#include "string.h"

//#include "delay.h"
#include "motor.h"
#include "OLED.h"
#include "JY61P.h"
#include "Delay.h"
#include "EMM.h"
#include "uart_my.h"
#include "track.h"
#include "key.h"
/*定义变量*/
extern float Target, Actuala,Actualb;			//目标值，实际值，输出值
extern float Kp, Ki, Kd;					//比例项，积分项，微分项的权重
extern float Error0a, Error1a, ErrorInta,Error0b, Error1b, ErrorIntb;		//本次误差，上次误差，误差积分
extern int Outa,Outb;
extern uint8_t T;
extern uint16_t car_state;
extern float E,E_finally,E0,E1,EI;
extern int result;

/*按键变量*/
extern uint8_t model_switch;
extern uint8_t K1,K1_last;
extern uint8_t K2,K2_last;
#endif