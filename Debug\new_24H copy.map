******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Fri Aug  1 17:52:16 2025

OUTPUT FILE NAME:   <new_24H copy.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00003c7d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00005290  0001ad70  R  X
  SRAM                  20200000   00008000  000007aa  00007856  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00005290   00005290    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00004260   00004260    r-x .text
  00004320    00004320    00000f20   00000f20    r-- .rodata
  00005240    00005240    00000050   00000050    r-- .cinit
20200000    20200000    000005aa   00000000    rw-
  20200000    20200000    0000057c   00000000    rw- .bss
  2020057c    2020057c    0000002e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00004260     
                  000000c0    00000a00     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000ac0    000002b8     OLED.o (.text.OLED_ShowImage)
                  00000d78    000002b4     JY61P.o (.text.UART2_IRQHandler)
                  0000102c    00000230     main.o (.text.Track_Err)
                  0000125c    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000147c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  00001658    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000017ea    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000017ec    00000188     track.o (.text.No_MCU_Ganv_Sensor_Init)
                  00001974    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00001ab0    00000120            : _printfi.c.obj (.text._pconv_e)
                  00001bd0    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001cdc    000000ec     main.o (.text.TIMG6_IRQHandler)
                  00001dc8    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001eb0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001f94    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002070    000000dc     OLED.o (.text.OLED_ClearArea)
                  0000214c    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002228    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002300    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  000023d8    000000d0     track.o (.text.Get_Analog_value)
                  000024a8    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  0000256c    000000c0     main.o (.text.main)
                  0000262c    000000c0     motor.o (.text.set_pwm)
                  000026ec    000000b0     OLED.o (.text.OLED_ShowChar)
                  0000279c    000000aa     track.o (.text.normalizeAnalogValues)
                  00002846    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00002848    000000a2                            : udivmoddi4.S.obj (.text)
                  000028ea    00000002     --HOLE-- [fill = 0]
                  000028ec    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000298c    0000009a     OLED.o (.text.OLED_Init)
                  00002a26    00000002     --HOLE-- [fill = 0]
                  00002a28    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00002ab4    00000088     key.o (.text.key_scan)
                  00002b3c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00002bbe    00000002     --HOLE-- [fill = 0]
                  00002bc0    00000080     driverlib.a : dl_timer.o (.text.DL_TimerA_initPWMMode)
                  00002c40    00000080     ti_msp_dl_config.o (.text.SYSCFG_DL_PWMA_init)
                  00002cc0    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002d38    00000072     track.o (.text.No_MCU_Ganv_Sensor_Init_Frist)
                  00002daa    00000002     --HOLE-- [fill = 0]
                  00002dac    00000070     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002e1c    0000006c                            : comparedf2.c.obj (.text.__ledf2)
                  00002e88    0000006c     track.o (.text.convertAnalogToDigital)
                  00002ef4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002f5a    00000002     --HOLE-- [fill = 0]
                  00002f5c    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00002fbe    00000002     --HOLE-- [fill = 0]
                  00002fc0    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00003022    00000002     --HOLE-- [fill = 0]
                  00003024    0000005c     main.o (.text.TIMG0_IRQHandler)
                  00003080    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000030dc    00000058     OLED.o (.text.OLED_Clear)
                  00003134    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000318c    00000058            : _printfi.c.obj (.text._pconv_f)
                  000031e4    00000056     OLED.o (.text.OLED_ShowString)
                  0000323a    00000002     --HOLE-- [fill = 0]
                  0000323c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00003292    00000054     OLED.o (.text.OLED_I2C_SendByte)
                  000032e6    00000002     --HOLE-- [fill = 0]
                  000032e8    00000054     OLED.o (.text.OLED_Update)
                  0000333c    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000338e    00000050     OLED.o (.text.OLED_WriteData)
                  000033de    00000002     --HOLE-- [fill = 0]
                  000033e0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000342c    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_JY61P_init)
                  00003478    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000034c2    0000004a     OLED.o (.text.OLED_GPIO_Init)
                  0000350c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00003556    00000002     --HOLE-- [fill = 0]
                  00003558    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000035a0    00000048     ADC.o (.text.adc_getValue)
                  000035e8    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  0000362c    00000042     track.o (.text.No_Mcu_Ganv_Sensor_Task_Without_tick)
                  0000366e    00000002     --HOLE-- [fill = 0]
                  00003670    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000036b0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC1_init)
                  000036f0    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00003730    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00003770    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000037b0    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000037f0    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00003830    0000003e     OLED.o (.text.OLED_Printf)
                  0000386e    00000002     --HOLE-- [fill = 0]
                  00003870    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000038ac    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  000038e8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00003924    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003960    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000399a    00000002     --HOLE-- [fill = 0]
                  0000399c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  000039d6    00000002     --HOLE-- [fill = 0]
                  000039d8    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00003a10    00000036     OLED.o (.text.OLED_SetCursor)
                  00003a46    00000002     --HOLE-- [fill = 0]
                  00003a48    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003a7c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003ab0    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_control_init)
                  00003ae4    00000030     ADC.o (.text.DL_ADC12_getMemResult)
                  00003b14    00000030     OLED.o (.text.OLED_W_SCL)
                  00003b44    00000030     OLED.o (.text.OLED_W_SDA)
                  00003b74    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00003ba4    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00003bd0    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003bfc    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00003c28    0000002a     OLED.o (.text.OLED_WriteCommand)
                  00003c52    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00003c7a    00000002     --HOLE-- [fill = 0]
                  00003c7c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00003ca4    00000024     OLED.o (.text.OLED_I2C_Start)
                  00003cc8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  00003cec    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00003d10    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00003d32    00000002     --HOLE-- [fill = 0]
                  00003d34    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00003d54    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00003d74    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00003d92    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00003db0    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00003dce    00000002     --HOLE-- [fill = 0]
                  00003dd0    0000001c     ADC.o (.text.DL_ADC12_startConversion)
                  00003dec    0000001c     ADC.o (.text.DL_ADC12_stopConversion)
                  00003e08    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003e24    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003e40    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003e5c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00003e78    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00003e94    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00003eb0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003ecc    0000001c     OLED.o (.text.OLED_I2C_Stop)
                  00003ee8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00003f00    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00003f18    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003f30    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00003f48    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003f60    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003f78    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00003f90    00000018     motor.o (.text.DL_GPIO_setPins)
                  00003fa8    00000018     track.o (.text.DL_GPIO_setPins)
                  00003fc0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003fd8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00003ff0    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00004008    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00004020    00000018     main.o (.text.DL_Timer_startCounter)
                  00004038    00000018     motor.o (.text.DL_Timer_startCounter)
                  00004050    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00004068    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00004080    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00004098    00000016     ADC.o (.text.DL_ADC12_disableConversions)
                  000040ae    00000016     ADC.o (.text.DL_ADC12_enableConversions)
                  000040c4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000040da    00000016     key.o (.text.DL_GPIO_readPins)
                  000040f0    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00004106    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  0000411a    00000014     motor.o (.text.DL_GPIO_clearPins)
                  0000412e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00004142    00000014     track.o (.text.DL_GPIO_clearPins)
                  00004156    00000002     --HOLE-- [fill = 0]
                  00004158    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000416c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00004180    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00004194    00000014     JY61P.o (.text.DL_UART_receiveData)
                  000041a8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000041bc    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000041d0    00000012     main.o (.text.DL_Timer_getPendingInterrupt)
                  000041e2    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  000041f4    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00004206    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00004218    00000010     ADC.o (.text.DL_ADC12_getStatus)
                  00004228    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00004238    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00004248    00000010     motor.o (.text.motor_init)
                  00004258    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00004268    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00004278    0000000e     track.o (.text.Get_Digtal_For_User)
                  00004286    00000002     --HOLE-- [fill = 0]
                  00004288    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00004296    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000042a4    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000042b2    00000002     --HOLE-- [fill = 0]
                  000042b4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  000042c0    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000042ca    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000042d4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  000042e4    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  000042ee    0000000a            : vsprintf.c.obj (.text._outc)
                  000042f8    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00004300    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00004308    00000008     libc.a : abs.c.obj (.text.abs)
                  00004310    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004314    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00004318    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000431c    00000004            : exit.c.obj (.text:abort)

.cinit     0    00005240    00000050     
                  00005240    0000002b     (.cinit..data.load) [load image, compression = lzss]
                  0000526b    00000001     --HOLE-- [fill = 0]
                  0000526c    0000000c     (__TI_handler_table)
                  00005278    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00005280    00000010     (__TI_cinit_table)

.rodata    0    00004320    00000f20     
                  00004320    000005f0     OLED_DATA.o (.rodata.OLED_F8x16)
                  00004910    00000532     OLED_DATA.o (.rodata.OLED_F7x12)
                  00004e42    0000023a     OLED_DATA.o (.rodata.OLED_F6x8)
                  0000507c    00000003     ti_msp_dl_config.o (.rodata.gPWMAClockConfig)
                  0000507f    00000001     --HOLE-- [fill = 0]
                  00005080    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00005181    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00005184    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000051ac    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000051c0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_controlTimerConfig)
                  000051d4    00000011     libc.a : _printfi.c.obj (.rodata.str1.11645776875810915891)
                  000051e5    00000011            : _printfi.c.obj (.rodata.str1.44690500295887128011)
                  000051f6    0000000b     main.o (.rodata.str1.176328792963337784071)
                  00005201    00000001     --HOLE-- [fill = 0]
                  00005202    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000520c    0000000a     ti_msp_dl_config.o (.rodata.gUART_JY61PConfig)
                  00005216    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00005218    00000008     ti_msp_dl_config.o (.rodata.gADC1ClockConfig)
                  00005220    00000008     ti_msp_dl_config.o (.rodata.gPWMAConfig)
                  00005228    00000008     main.o (.rodata.str1.97993385775340092391)
                  00005230    00000007     main.o (.rodata.str1.52501554851255701521)
                  00005237    00000003     ti_msp_dl_config.o (.rodata.gTIMER_controlClockConfig)
                  0000523a    00000002     ti_msp_dl_config.o (.rodata.gUART_JY61PClockConfig)
                  0000523c    00000004     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    0000057c     UNINITIALIZED
                  20200000    00000400     (.common:OLED_DisplayBuf)
                  20200400    000000bc     (.common:gPWMABackup)
                  202004bc    00000078     (.common:gTIMER_controlBackup)
                  20200534    00000009     (.common:receivedData)
                  2020053d    00000001     (.common:Digtal)
                  2020053e    00000001     (.common:K1)
                  2020053f    00000001     (.common:K1_last)
                  20200540    00000004     (.common:Actual_err)
                  20200544    00000004     (.common:Err)
                  20200548    00000004     (.common:Error0_err)
                  2020054c    00000004     (.common:Error1_err)
                  20200550    00000004     (.common:ErrorInt_err)
                  20200554    00000004     (.common:Pitch)
                  20200558    00000004     (.common:Roll)
                  2020055c    00000004     (.common:Target_err)
                  20200560    00000004     (.common:Yaw)
                  20200564    00000004     (.common:last_xun)
                  20200568    00000004     (.common:result)
                  2020056c    00000004     (.common:xun)
                  20200570    00000001     (.common:K2)
                  20200571    00000001     (.common:K2_last)
                  20200572    00000001     (.common:PitchH)
                  20200573    00000001     (.common:PitchL)
                  20200574    00000001     (.common:RollH)
                  20200575    00000001     (.common:RollL)
                  20200576    00000001     (.common:SUM)
                  20200577    00000001     (.common:VH)
                  20200578    00000001     (.common:VL)
                  20200579    00000001     (.common:YawH)
                  2020057a    00000001     (.common:YawL)
                  2020057b    00000001     (.common:model_switch)

.data      0    2020057c    0000002e     UNINITIALIZED
                  2020057c    00000010     main.o (.data.black)
                  2020058c    00000010     main.o (.data.white)
                  2020059c    00000004     main.o (.data.Count1)
                  202005a0    00000004     OLED.o (.data.FPS)
                  202005a4    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202005a8    00000001     JY61P.o (.data.RxState)
                  202005a9    00000001     JY61P.o (.data.dataIndex)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             1850    129       308    
       main.o                         1166    26        73     
       track.o                        1116    0         0      
       motor.o                        276     0         0      
       ADC.o                          236     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       key.o                          158     0         5      
    +--+------------------------------+-------+---------+---------+
       Total:                         4810    347       386    
                                                               
    .\OLED\
       OLED_DATA.o                    0       3420      0      
       OLED.o                         2104    0         1028   
    +--+------------------------------+-------+---------+---------+
       Total:                         2104    3420      1028   
                                                               
    .\USART_JY61P\
       JY61P.o                        712     0         32     
    +--+------------------------------+-------+---------+---------+
       Total:                         712     0         32     
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     652     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      90      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1104    0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4558    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     120     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       abs.c.obj                      8       0         0      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5456    291       4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2762    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       79        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   16948   4137      1962   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00005280 records: 2, size/record: 8, table size: 16
	.data: load addr=00005240, load size=0000002b bytes, run addr=2020057c, run size=0000002e bytes, compression=lzss
	.bss: load addr=00005278, load size=00000008 bytes, run addr=20200000, run size=0000057c bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 0000526c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00001659     000042d4     000042d2   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
00004311  ADC0_IRQHandler                      
00004311  ADC1_IRQHandler                      
00004311  AES_IRQHandler                       
20200540  Actual_err                           
0000431c  C$$EXIT                              
00004311  CANFD0_IRQHandler                    
2020059c  Count1                               
00004311  DAC0_IRQHandler                      
00003671  DL_ADC12_setClockConfig              
000042c1  DL_Common_delayCycles                
00001f95  DL_SYSCTL_configSYSPLL               
000035e9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002bc1  DL_TimerA_initPWMMode                
000024a9  DL_Timer_initPWMMode                 
00001dc9  DL_Timer_initTimerMode               
00003e79  DL_Timer_setCaptCompUpdateMethod     
00004009  DL_Timer_setCaptureCompareOutCtl     
00004239  DL_Timer_setCaptureCompareValue      
00003e95  DL_Timer_setClockConfig              
00003559  DL_UART_init                         
000041e3  DL_UART_setClockConfig               
00004311  DMA_IRQHandler                       
00004311  Default_Handler                      
2020053d  Digtal                               
20200544  Err                                  
20200548  Error0_err                           
2020054c  Error1_err                           
20200550  ErrorInt_err                         
202005a0  FPS                                  
00004311  GROUP0_IRQHandler                    
00004311  GROUP1_IRQHandler                    
000023d9  Get_Analog_value                     
00004279  Get_Digtal_For_User                  
00004311  HardFault_Handler                    
00004311  I2C0_IRQHandler                      
00004311  I2C1_IRQHandler                      
2020053e  K1                                   
2020053f  K1_last                              
20200570  K2                                   
20200571  K2_last                              
00004311  NMI_Handler                          
000017ed  No_MCU_Ganv_Sensor_Init              
00002d39  No_MCU_Ganv_Sensor_Init_Frist        
0000362d  No_Mcu_Ganv_Sensor_Task_Without_tick 
000030dd  OLED_Clear                           
00002071  OLED_ClearArea                       
20200000  OLED_DisplayBuf                      
00004e42  OLED_F6x8                            
00004910  OLED_F7x12                           
00004320  OLED_F8x16                           
000034c3  OLED_GPIO_Init                       
00003293  OLED_I2C_SendByte                    
00003ca5  OLED_I2C_Start                       
00003ecd  OLED_I2C_Stop                        
0000298d  OLED_Init                            
00003831  OLED_Printf                          
00003a11  OLED_SetCursor                       
000026ed  OLED_ShowChar                        
00000ac1  OLED_ShowImage                       
000031e5  OLED_ShowString                      
000032e9  OLED_Update                          
00003b15  OLED_W_SCL                           
00003b45  OLED_W_SDA                           
00003c29  OLED_WriteCommand                    
0000338f  OLED_WriteData                       
00004311  PendSV_Handler                       
20200554  Pitch                                
20200572  PitchH                               
20200573  PitchL                               
00004311  RTC_IRQHandler                       
00004315  Reset_Handler                        
20200558  Roll                                 
20200574  RollH                                
20200575  RollL                                
202005a8  RxState                              
00004311  SPI0_IRQHandler                      
00004311  SPI1_IRQHandler                      
20200576  SUM                                  
00004311  SVC_Handler                          
000036b1  SYSCFG_DL_ADC1_init                  
0000214d  SYSCFG_DL_GPIO_init                  
00002c41  SYSCFG_DL_PWMA_init                  
00003871  SYSCFG_DL_SYSCTL_init                
00003a7d  SYSCFG_DL_TIMER_0_init               
00003ab1  SYSCFG_DL_TIMER_control_init         
000036f1  SYSCFG_DL_UART_0_init                
0000342d  SYSCFG_DL_UART_JY61P_init            
00003731  SYSCFG_DL_init                       
000028ed  SYSCFG_DL_initPower                  
00004311  SysTick_Handler                      
00004311  TIMA0_IRQHandler                     
00004311  TIMA1_IRQHandler                     
00003025  TIMG0_IRQHandler                     
00004311  TIMG12_IRQHandler                    
00001cdd  TIMG6_IRQHandler                     
00004311  TIMG7_IRQHandler                     
00004311  TIMG8_IRQHandler                     
000041f5  TI_memcpy_small                      
000042a5  TI_memset_small                      
2020055c  Target_err                           
0000102d  Track_Err                            
00004311  UART0_IRQHandler                     
00004311  UART1_IRQHandler                     
00000d79  UART2_IRQHandler                     
00004311  UART3_IRQHandler                     
20200577  VH                                   
20200578  VL                                   
20200560  Yaw                                  
20200579  YawH                                 
2020057a  YawL                                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00005280  __TI_CINIT_Base                      
00005290  __TI_CINIT_Limit                     
00005290  __TI_CINIT_Warm                      
0000526c  __TI_Handler_Table_Base              
00005278  __TI_Handler_Table_Limit             
00003925  __TI_auto_init_nobinit_nopinit       
00002cc1  __TI_decompress_lzss                 
00004207  __TI_decompress_none                 
00003135  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00004269  __TI_zero_init                       
00001663  __adddf3                             
0000230b  __addsf3                             
00005080  __aeabi_ctype_table_                 
00005080  __aeabi_ctype_table_C                
0000350d  __aeabi_d2iz                         
00001663  __aeabi_dadd                         
00002f5d  __aeabi_dcmpeq                       
00002f99  __aeabi_dcmpge                       
00002fad  __aeabi_dcmpgt                       
00002f85  __aeabi_dcmple                       
00002f71  __aeabi_dcmplt                       
00001bd1  __aeabi_ddiv                         
00001eb1  __aeabi_dmul                         
00001659  __aeabi_dsub                         
202005a4  __aeabi_errno                        
000042f9  __aeabi_errno_addr                   
000037b1  __aeabi_f2d                          
000039d9  __aeabi_f2iz                         
0000230b  __aeabi_fadd                         
00002fc1  __aeabi_fcmpeq                       
00002ffd  __aeabi_fcmpge                       
00003011  __aeabi_fcmpgt                       
00002fe9  __aeabi_fcmple                       
00002fd5  __aeabi_fcmplt                       
00002b3d  __aeabi_fdiv                         
00002a29  __aeabi_fmul                         
00002301  __aeabi_fsub                         
00003bd1  __aeabi_i2d                          
000038ad  __aeabi_i2f                          
0000323d  __aeabi_idiv                         
000017eb  __aeabi_idiv0                        
0000323d  __aeabi_idivmod                      
00002847  __aeabi_ldiv0                        
00003db1  __aeabi_llsl                         
00003ced  __aeabi_lmul                         
000042b5  __aeabi_memclr                       
000042b5  __aeabi_memclr4                      
000042b5  __aeabi_memclr8                      
00004301  __aeabi_memcpy                       
00004301  __aeabi_memcpy4                      
00004301  __aeabi_memcpy8                      
00004289  __aeabi_memset                       
00004289  __aeabi_memset4                      
00004289  __aeabi_memset8                      
00003cc9  __aeabi_ui2d                         
00003771  __aeabi_uidiv                        
00003771  __aeabi_uidivmod                     
000041a9  __aeabi_uldivmod                     
00003db1  __ashldi3                            
ffffffff  __binit__                            
00002e1d  __cmpdf2                             
00003961  __cmpsf2                             
00001bd1  __divdf3                             
00002b3d  __divsf3                             
00002e1d  __eqdf2                              
00003961  __eqsf2                              
000037b1  __extendsfdf2                        
0000350d  __fixdfsi                            
000039d9  __fixsfsi                            
00003bd1  __floatsidf                          
000038ad  __floatsisf                          
00003cc9  __floatunsidf                        
00002dad  __gedf2                              
000038e9  __gesf2                              
00002dad  __gtdf2                              
000038e9  __gtsf2                              
00002e1d  __ledf2                              
00003961  __lesf2                              
00002e1d  __ltdf2                              
00003961  __ltsf2                              
UNDEFED   __mpu_init                           
00001eb1  __muldf3                             
00003ced  __muldi3                             
0000399d  __muldsi3                            
00002a29  __mulsf3                             
00002e1d  __nedf2                              
00003961  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
00001659  __subdf3                             
00002301  __subsf3                             
00002849  __udivmoddi4                         
00003c7d  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00004319  _system_pre_init                     
0000431d  abort                                
00004309  abs                                  
000035a1  adc_getValue                         
000037f1  atoi                                 
ffffffff  binit                                
2020057c  black                                
00002e89  convertAnalogToDigital               
202005a9  dataIndex                            
00003081  frexp                                
00003081  frexpl                               
20200400  gPWMABackup                          
202004bc  gTIMER_controlBackup                 
00000000  interruptVectors                     
00002ab5  key_scan                             
20200564  last_xun                             
00002229  ldexp                                
00002229  ldexpl                               
0000256d  main                                 
00003d11  memccpy                              
2020057b  model_switch                         
00004249  motor_init                           
0000279d  normalizeAnalogValues                
20200534  receivedData                         
20200568  result                               
00002229  scalbn                               
00002229  scalbnl                              
0000262d  set_pwm                              
00003bfd  vsprintf                             
00004259  wcslen                               
2020058c  white                                
2020056c  xun                                  


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000ac1  OLED_ShowImage                       
00000d79  UART2_IRQHandler                     
0000102d  Track_Err                            
00001659  __aeabi_dsub                         
00001659  __subdf3                             
00001663  __adddf3                             
00001663  __aeabi_dadd                         
000017eb  __aeabi_idiv0                        
000017ed  No_MCU_Ganv_Sensor_Init              
00001bd1  __aeabi_ddiv                         
00001bd1  __divdf3                             
00001cdd  TIMG6_IRQHandler                     
00001dc9  DL_Timer_initTimerMode               
00001eb1  __aeabi_dmul                         
00001eb1  __muldf3                             
00001f95  DL_SYSCTL_configSYSPLL               
00002071  OLED_ClearArea                       
0000214d  SYSCFG_DL_GPIO_init                  
00002229  ldexp                                
00002229  ldexpl                               
00002229  scalbn                               
00002229  scalbnl                              
00002301  __aeabi_fsub                         
00002301  __subsf3                             
0000230b  __addsf3                             
0000230b  __aeabi_fadd                         
000023d9  Get_Analog_value                     
000024a9  DL_Timer_initPWMMode                 
0000256d  main                                 
0000262d  set_pwm                              
000026ed  OLED_ShowChar                        
0000279d  normalizeAnalogValues                
00002847  __aeabi_ldiv0                        
00002849  __udivmoddi4                         
000028ed  SYSCFG_DL_initPower                  
0000298d  OLED_Init                            
00002a29  __aeabi_fmul                         
00002a29  __mulsf3                             
00002ab5  key_scan                             
00002b3d  __aeabi_fdiv                         
00002b3d  __divsf3                             
00002bc1  DL_TimerA_initPWMMode                
00002c41  SYSCFG_DL_PWMA_init                  
00002cc1  __TI_decompress_lzss                 
00002d39  No_MCU_Ganv_Sensor_Init_Frist        
00002dad  __gedf2                              
00002dad  __gtdf2                              
00002e1d  __cmpdf2                             
00002e1d  __eqdf2                              
00002e1d  __ledf2                              
00002e1d  __ltdf2                              
00002e1d  __nedf2                              
00002e89  convertAnalogToDigital               
00002f5d  __aeabi_dcmpeq                       
00002f71  __aeabi_dcmplt                       
00002f85  __aeabi_dcmple                       
00002f99  __aeabi_dcmpge                       
00002fad  __aeabi_dcmpgt                       
00002fc1  __aeabi_fcmpeq                       
00002fd5  __aeabi_fcmplt                       
00002fe9  __aeabi_fcmple                       
00002ffd  __aeabi_fcmpge                       
00003011  __aeabi_fcmpgt                       
00003025  TIMG0_IRQHandler                     
00003081  frexp                                
00003081  frexpl                               
000030dd  OLED_Clear                           
00003135  __TI_ltoa                            
000031e5  OLED_ShowString                      
0000323d  __aeabi_idiv                         
0000323d  __aeabi_idivmod                      
00003293  OLED_I2C_SendByte                    
000032e9  OLED_Update                          
0000338f  OLED_WriteData                       
0000342d  SYSCFG_DL_UART_JY61P_init            
000034c3  OLED_GPIO_Init                       
0000350d  __aeabi_d2iz                         
0000350d  __fixdfsi                            
00003559  DL_UART_init                         
000035a1  adc_getValue                         
000035e9  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
0000362d  No_Mcu_Ganv_Sensor_Task_Without_tick 
00003671  DL_ADC12_setClockConfig              
000036b1  SYSCFG_DL_ADC1_init                  
000036f1  SYSCFG_DL_UART_0_init                
00003731  SYSCFG_DL_init                       
00003771  __aeabi_uidiv                        
00003771  __aeabi_uidivmod                     
000037b1  __aeabi_f2d                          
000037b1  __extendsfdf2                        
000037f1  atoi                                 
00003831  OLED_Printf                          
00003871  SYSCFG_DL_SYSCTL_init                
000038ad  __aeabi_i2f                          
000038ad  __floatsisf                          
000038e9  __gesf2                              
000038e9  __gtsf2                              
00003925  __TI_auto_init_nobinit_nopinit       
00003961  __cmpsf2                             
00003961  __eqsf2                              
00003961  __lesf2                              
00003961  __ltsf2                              
00003961  __nesf2                              
0000399d  __muldsi3                            
000039d9  __aeabi_f2iz                         
000039d9  __fixsfsi                            
00003a11  OLED_SetCursor                       
00003a7d  SYSCFG_DL_TIMER_0_init               
00003ab1  SYSCFG_DL_TIMER_control_init         
00003b15  OLED_W_SCL                           
00003b45  OLED_W_SDA                           
00003bd1  __aeabi_i2d                          
00003bd1  __floatsidf                          
00003bfd  vsprintf                             
00003c29  OLED_WriteCommand                    
00003c7d  _c_int00_noargs                      
00003ca5  OLED_I2C_Start                       
00003cc9  __aeabi_ui2d                         
00003cc9  __floatunsidf                        
00003ced  __aeabi_lmul                         
00003ced  __muldi3                             
00003d11  memccpy                              
00003db1  __aeabi_llsl                         
00003db1  __ashldi3                            
00003e79  DL_Timer_setCaptCompUpdateMethod     
00003e95  DL_Timer_setClockConfig              
00003ecd  OLED_I2C_Stop                        
00004009  DL_Timer_setCaptureCompareOutCtl     
000041a9  __aeabi_uldivmod                     
000041e3  DL_UART_setClockConfig               
000041f5  TI_memcpy_small                      
00004207  __TI_decompress_none                 
00004239  DL_Timer_setCaptureCompareValue      
00004249  motor_init                           
00004259  wcslen                               
00004269  __TI_zero_init                       
00004279  Get_Digtal_For_User                  
00004289  __aeabi_memset                       
00004289  __aeabi_memset4                      
00004289  __aeabi_memset8                      
000042a5  TI_memset_small                      
000042b5  __aeabi_memclr                       
000042b5  __aeabi_memclr4                      
000042b5  __aeabi_memclr8                      
000042c1  DL_Common_delayCycles                
000042f9  __aeabi_errno_addr                   
00004301  __aeabi_memcpy                       
00004301  __aeabi_memcpy4                      
00004301  __aeabi_memcpy8                      
00004309  abs                                  
00004311  ADC0_IRQHandler                      
00004311  ADC1_IRQHandler                      
00004311  AES_IRQHandler                       
00004311  CANFD0_IRQHandler                    
00004311  DAC0_IRQHandler                      
00004311  DMA_IRQHandler                       
00004311  Default_Handler                      
00004311  GROUP0_IRQHandler                    
00004311  GROUP1_IRQHandler                    
00004311  HardFault_Handler                    
00004311  I2C0_IRQHandler                      
00004311  I2C1_IRQHandler                      
00004311  NMI_Handler                          
00004311  PendSV_Handler                       
00004311  RTC_IRQHandler                       
00004311  SPI0_IRQHandler                      
00004311  SPI1_IRQHandler                      
00004311  SVC_Handler                          
00004311  SysTick_Handler                      
00004311  TIMA0_IRQHandler                     
00004311  TIMA1_IRQHandler                     
00004311  TIMG12_IRQHandler                    
00004311  TIMG7_IRQHandler                     
00004311  TIMG8_IRQHandler                     
00004311  UART0_IRQHandler                     
00004311  UART1_IRQHandler                     
00004311  UART3_IRQHandler                     
00004315  Reset_Handler                        
00004319  _system_pre_init                     
0000431c  C$$EXIT                              
0000431d  abort                                
00004320  OLED_F8x16                           
00004910  OLED_F7x12                           
00004e42  OLED_F6x8                            
00005080  __aeabi_ctype_table_                 
00005080  __aeabi_ctype_table_C                
0000526c  __TI_Handler_Table_Base              
00005278  __TI_Handler_Table_Limit             
00005280  __TI_CINIT_Base                      
00005290  __TI_CINIT_Limit                     
00005290  __TI_CINIT_Warm                      
20200000  OLED_DisplayBuf                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200400  gPWMABackup                          
202004bc  gTIMER_controlBackup                 
20200534  receivedData                         
2020053d  Digtal                               
2020053e  K1                                   
2020053f  K1_last                              
20200540  Actual_err                           
20200544  Err                                  
20200548  Error0_err                           
2020054c  Error1_err                           
20200550  ErrorInt_err                         
20200554  Pitch                                
20200558  Roll                                 
2020055c  Target_err                           
20200560  Yaw                                  
20200564  last_xun                             
20200568  result                               
2020056c  xun                                  
20200570  K2                                   
20200571  K2_last                              
20200572  PitchH                               
20200573  PitchL                               
20200574  RollH                                
20200575  RollL                                
20200576  SUM                                  
20200577  VH                                   
20200578  VL                                   
20200579  YawH                                 
2020057a  YawL                                 
2020057b  model_switch                         
2020057c  black                                
2020058c  white                                
2020059c  Count1                               
202005a0  FPS                                  
202005a4  __aeabi_errno                        
202005a8  RxState                              
202005a9  dataIndex                            
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[255 symbols]
