#include "OLED_DATA.h"

/**
  * 数据存储格式：
  * 纵向8点，高位在下，先从左到右，再从上到下
  * 每一个Bit对应一个像素点
  * 
  *      B0 B0                  B0 B0
  *      B1 B1                  B1 B1
  *      B2 B2                  B2 B2
  *      B3 B3  ------------->  B3 B3 --
  *      B4 B4                  B4 B4  |
  *      B5 B5                  B5 B5  |
  *      B6 B6                  B6 B6  |
  *      B7 B7                  B7 B7  |
  *                                    |
  *  -----------------------------------
  *  |   
  *  |   B0 B0                  B0 B0
  *  |   B1 B1                  B1 B1
  *  |   B2 B2                  B2 B2
  *  --> B3 B3  ------------->  B3 B3
  *      B4 B4                  B4 B4
  *      B5 B5                  B5 B5
  *      B6 B6                  B6 B6
  *      B7 B7                  B7 B7
  * 
  */

/*ASCII字模数据*********************/

/*宽8像素，高16像素*/
const uint8_t OLED_F8x16[][16] =
{
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//   0
	0x00,0x00,0x00,0xF8,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x33,0x30,0x00,0x00,0x00,// ! 1
	0x00,0x16,0x0E,0x00,0x16,0x0E,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,// " 2
	0x40,0xC0,0x78,0x40,0xC0,0x78,0x40,0x00,
	0x04,0x3F,0x04,0x04,0x3F,0x04,0x04,0x00,// # 3
	0x00,0x70,0x88,0xFC,0x08,0x30,0x00,0x00,
	0x00,0x18,0x20,0xFF,0x21,0x1E,0x00,0x00,// $ 4
	0xF0,0x08,0xF0,0x00,0xE0,0x18,0x00,0x00,
	0x00,0x21,0x1C,0x03,0x1E,0x21,0x1E,0x00,// % 5
	0x00,0xF0,0x08,0x88,0x70,0x00,0x00,0x00,
	0x1E,0x21,0x23,0x24,0x19,0x27,0x21,0x10,// & 6
	0x00,0x00,0x00,0x16,0x0E,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,// ' 7
	0x00,0x00,0x00,0xE0,0x18,0x04,0x02,0x00,
	0x00,0x00,0x00,0x07,0x18,0x20,0x40,0x00,// ( 8
	0x00,0x02,0x04,0x18,0xE0,0x00,0x00,0x00,
	0x00,0x40,0x20,0x18,0x07,0x00,0x00,0x00,// ) 9
	0x40,0x40,0x80,0xF0,0x80,0x40,0x40,0x00,
	0x02,0x02,0x01,0x0F,0x01,0x02,0x02,0x00,// * 10
	0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,
	0x01,0x01,0x01,0x1F,0x01,0x01,0x01,0x00,// + 11
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0xB0,0x70,0x00,0x00,0x00,0x00,0x00,// , 12
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,// - 13
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x30,0x30,0x00,0x00,0x00,0x00,0x00,// . 14
	0x00,0x00,0x00,0x00,0x80,0x60,0x18,0x04,
	0x00,0x60,0x18,0x06,0x01,0x00,0x00,0x00,// / 15
	0x00,0xE0,0x10,0x08,0x08,0x10,0xE0,0x00,
	0x00,0x0F,0x10,0x20,0x20,0x10,0x0F,0x00,// 0 16
	0x00,0x10,0x10,0xF8,0x00,0x00,0x00,0x00,
	0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,// 1 17
	0x00,0x70,0x08,0x08,0x08,0x88,0x70,0x00,
	0x00,0x30,0x28,0x24,0x22,0x21,0x30,0x00,// 2 18
	0x00,0x30,0x08,0x88,0x88,0x48,0x30,0x00,
	0x00,0x18,0x20,0x20,0x20,0x11,0x0E,0x00,// 3 19
	0x00,0x00,0xC0,0x20,0x10,0xF8,0x00,0x00,
	0x00,0x07,0x04,0x24,0x24,0x3F,0x24,0x00,// 4 20
	0x00,0xF8,0x08,0x88,0x88,0x08,0x08,0x00,
	0x00,0x19,0x21,0x20,0x20,0x11,0x0E,0x00,// 5 21
	0x00,0xE0,0x10,0x88,0x88,0x18,0x00,0x00,
	0x00,0x0F,0x11,0x20,0x20,0x11,0x0E,0x00,// 6 22
	0x00,0x38,0x08,0x08,0xC8,0x38,0x08,0x00,
	0x00,0x00,0x00,0x3F,0x00,0x00,0x00,0x00,// 7 23
	0x00,0x70,0x88,0x08,0x08,0x88,0x70,0x00,
	0x00,0x1C,0x22,0x21,0x21,0x22,0x1C,0x00,// 8 24
	0x00,0xE0,0x10,0x08,0x08,0x10,0xE0,0x00,
	0x00,0x00,0x31,0x22,0x22,0x11,0x0F,0x00,// 9 25
	0x00,0x00,0x00,0xC0,0xC0,0x00,0x00,0x00,
	0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,// : 26
	0x00,0x00,0x00,0xC0,0xC0,0x00,0x00,0x00,
	0x00,0x00,0x80,0xB0,0x70,0x00,0x00,0x00,// ; 27
	0x00,0x00,0x80,0x40,0x20,0x10,0x08,0x00,
	0x00,0x01,0x02,0x04,0x08,0x10,0x20,0x00,// < 28
	0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,
	0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x00,// = 29
	0x00,0x08,0x10,0x20,0x40,0x80,0x00,0x00,
	0x00,0x20,0x10,0x08,0x04,0x02,0x01,0x00,// > 30
	0x00,0x70,0x48,0x08,0x08,0x08,0xF0,0x00,
	0x00,0x00,0x00,0x30,0x36,0x01,0x00,0x00,// ? 31
	0xC0,0x30,0xC8,0x28,0xE8,0x10,0xE0,0x00,
	0x07,0x18,0x27,0x24,0x23,0x14,0x0B,0x00,// @ 32
	0x00,0x00,0xC0,0x38,0xE0,0x00,0x00,0x00,
	0x20,0x3C,0x23,0x02,0x02,0x27,0x38,0x20,// A 33
	0x08,0xF8,0x88,0x88,0x88,0x70,0x00,0x00,
	0x20,0x3F,0x20,0x20,0x20,0x11,0x0E,0x00,// B 34
	0xC0,0x30,0x08,0x08,0x08,0x08,0x38,0x00,
	0x07,0x18,0x20,0x20,0x20,0x10,0x08,0x00,// C 35
	0x08,0xF8,0x08,0x08,0x08,0x10,0xE0,0x00,
	0x20,0x3F,0x20,0x20,0x20,0x10,0x0F,0x00,// D 36
	0x08,0xF8,0x88,0x88,0xE8,0x08,0x10,0x00,
	0x20,0x3F,0x20,0x20,0x23,0x20,0x18,0x00,// E 37
	0x08,0xF8,0x88,0x88,0xE8,0x08,0x10,0x00,
	0x20,0x3F,0x20,0x00,0x03,0x00,0x00,0x00,// F 38
	0xC0,0x30,0x08,0x08,0x08,0x38,0x00,0x00,
	0x07,0x18,0x20,0x20,0x22,0x1E,0x02,0x00,// G 39
	0x08,0xF8,0x08,0x00,0x00,0x08,0xF8,0x08,
	0x20,0x3F,0x21,0x01,0x01,0x21,0x3F,0x20,// H 40
	0x00,0x08,0x08,0xF8,0x08,0x08,0x00,0x00,
	0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,// I 41
	0x00,0x00,0x08,0x08,0xF8,0x08,0x08,0x00,
	0xC0,0x80,0x80,0x80,0x7F,0x00,0x00,0x00,// J 42
	0x08,0xF8,0x88,0xC0,0x28,0x18,0x08,0x00,
	0x20,0x3F,0x20,0x01,0x26,0x38,0x20,0x00,// K 43
	0x08,0xF8,0x08,0x00,0x00,0x00,0x00,0x00,
	0x20,0x3F,0x20,0x20,0x20,0x20,0x30,0x00,// L 44
	0x08,0xF8,0xF8,0x00,0xF8,0xF8,0x08,0x00,
	0x20,0x3F,0x00,0x3F,0x00,0x3F,0x20,0x00,// M 45
	0x08,0xF8,0x30,0xC0,0x00,0x08,0xF8,0x08,
	0x20,0x3F,0x20,0x00,0x07,0x18,0x3F,0x00,// N 46
	0xE0,0x10,0x08,0x08,0x08,0x10,0xE0,0x00,
	0x0F,0x10,0x20,0x20,0x20,0x10,0x0F,0x00,// O 47
	0x08,0xF8,0x08,0x08,0x08,0x08,0xF0,0x00,
	0x20,0x3F,0x21,0x01,0x01,0x01,0x00,0x00,// P 48
	0xE0,0x10,0x08,0x08,0x08,0x10,0xE0,0x00,
	0x0F,0x18,0x24,0x24,0x38,0x50,0x4F,0x00,// Q 49
	0x08,0xF8,0x88,0x88,0x88,0x88,0x70,0x00,
	0x20,0x3F,0x20,0x00,0x03,0x0C,0x30,0x20,// R 50
	0x00,0x70,0x88,0x08,0x08,0x08,0x38,0x00,
	0x00,0x38,0x20,0x21,0x21,0x22,0x1C,0x00,// S 51
	0x18,0x08,0x08,0xF8,0x08,0x08,0x18,0x00,
	0x00,0x00,0x20,0x3F,0x20,0x00,0x00,0x00,// T 52
	0x08,0xF8,0x08,0x00,0x00,0x08,0xF8,0x08,
	0x00,0x1F,0x20,0x20,0x20,0x20,0x1F,0x00,// U 53
	0x08,0x78,0x88,0x00,0x00,0xC8,0x38,0x08,
	0x00,0x00,0x07,0x38,0x0E,0x01,0x00,0x00,// V 54
	0xF8,0x08,0x00,0xF8,0x00,0x08,0xF8,0x00,
	0x03,0x3C,0x07,0x00,0x07,0x3C,0x03,0x00,// W 55
	0x08,0x18,0x68,0x80,0x80,0x68,0x18,0x08,
	0x20,0x30,0x2C,0x03,0x03,0x2C,0x30,0x20,// X 56
	0x08,0x38,0xC8,0x00,0xC8,0x38,0x08,0x00,
	0x00,0x00,0x20,0x3F,0x20,0x00,0x00,0x00,// Y 57
	0x10,0x08,0x08,0x08,0xC8,0x38,0x08,0x00,
	0x20,0x38,0x26,0x21,0x20,0x20,0x18,0x00,// Z 58
	0x00,0x00,0x00,0xFE,0x02,0x02,0x02,0x00,
	0x00,0x00,0x00,0x7F,0x40,0x40,0x40,0x00,// [ 59
	0x00,0x0C,0x30,0xC0,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x01,0x06,0x38,0xC0,0x00,// \ 60
	0x00,0x02,0x02,0x02,0xFE,0x00,0x00,0x00,
	0x00,0x40,0x40,0x40,0x7F,0x00,0x00,0x00,// ] 61
	0x00,0x20,0x10,0x08,0x04,0x08,0x10,0x20,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,// ^ 62
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,// _ 63
	0x00,0x02,0x04,0x08,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,// ` 64
	0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,
	0x00,0x19,0x24,0x22,0x22,0x22,0x3F,0x20,// a 65
	0x08,0xF8,0x00,0x80,0x80,0x00,0x00,0x00,
	0x00,0x3F,0x11,0x20,0x20,0x11,0x0E,0x00,// b 66
	0x00,0x00,0x00,0x80,0x80,0x80,0x00,0x00,
	0x00,0x0E,0x11,0x20,0x20,0x20,0x11,0x00,// c 67
	0x00,0x00,0x00,0x80,0x80,0x88,0xF8,0x00,
	0x00,0x0E,0x11,0x20,0x20,0x10,0x3F,0x20,// d 68
	0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,
	0x00,0x1F,0x22,0x22,0x22,0x22,0x13,0x00,// e 69
	0x00,0x80,0x80,0xF0,0x88,0x88,0x88,0x18,
	0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,// f 70
	0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x00,
	0x00,0x6B,0x94,0x94,0x94,0x93,0x60,0x00,// g 71
	0x08,0xF8,0x00,0x80,0x80,0x80,0x00,0x00,
	0x20,0x3F,0x21,0x00,0x00,0x20,0x3F,0x20,// h 72
	0x00,0x80,0x98,0x98,0x00,0x00,0x00,0x00,
	0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,// i 73
	0x00,0x00,0x00,0x80,0x98,0x98,0x00,0x00,
	0x00,0xC0,0x80,0x80,0x80,0x7F,0x00,0x00,// j 74
	0x08,0xF8,0x00,0x00,0x80,0x80,0x80,0x00,
	0x20,0x3F,0x24,0x02,0x2D,0x30,0x20,0x00,// k 75
	0x00,0x08,0x08,0xF8,0x00,0x00,0x00,0x00,
	0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,// l 76
	0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,
	0x20,0x3F,0x20,0x00,0x3F,0x20,0x00,0x3F,// m 77
	0x00,0x80,0x80,0x00,0x80,0x80,0x00,0x00,
	0x00,0x20,0x3F,0x21,0x00,0x20,0x3F,0x20,// n 78
	0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,
	0x00,0x1F,0x20,0x20,0x20,0x20,0x1F,0x00,// o 79
	0x80,0x80,0x00,0x80,0x80,0x00,0x00,0x00,
	0x80,0xFF,0xA1,0x20,0x20,0x11,0x0E,0x00,// p 80
	0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x00,
	0x00,0x0E,0x11,0x20,0x20,0xA0,0xFF,0x80,// q 81
	0x80,0x80,0x80,0x00,0x80,0x80,0x80,0x00,
	0x20,0x20,0x3F,0x21,0x20,0x00,0x01,0x00,// r 82
	0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x00,
	0x00,0x33,0x24,0x24,0x24,0x24,0x19,0x00,// s 83
	0x00,0x80,0x80,0xE0,0x80,0x80,0x00,0x00,
	0x00,0x00,0x00,0x1F,0x20,0x20,0x00,0x00,// t 84
	0x80,0x80,0x00,0x00,0x00,0x80,0x80,0x00,
	0x00,0x1F,0x20,0x20,0x20,0x10,0x3F,0x20,// u 85
	0x80,0x80,0x80,0x00,0x00,0x80,0x80,0x80,
	0x00,0x01,0x0E,0x30,0x08,0x06,0x01,0x00,// v 86
	0x80,0x80,0x00,0x80,0x00,0x80,0x80,0x80,
	0x0F,0x30,0x0C,0x03,0x0C,0x30,0x0F,0x00,// w 87
	0x00,0x80,0x80,0x00,0x80,0x80,0x80,0x00,
	0x00,0x20,0x31,0x2E,0x0E,0x31,0x20,0x00,// x 88
	0x80,0x80,0x80,0x00,0x00,0x80,0x80,0x80,
	0x80,0x81,0x8E,0x70,0x18,0x06,0x01,0x00,// y 89
	0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x00,
	0x00,0x21,0x30,0x2C,0x22,0x21,0x30,0x00,// z 90
	0x00,0x00,0x00,0x00,0x80,0x7C,0x02,0x02,
	0x00,0x00,0x00,0x00,0x00,0x3F,0x40,0x40,// { 91
	0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x00,// | 92
	0x00,0x02,0x02,0x7C,0x80,0x00,0x00,0x00,
	0x00,0x40,0x40,0x3F,0x00,0x00,0x00,0x00,// } 93
	0x00,0x80,0x40,0x40,0x80,0x00,0x00,0x80,
	0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x00,// ~ 94
};
const uint8_t OLED_F7x12[][14] = {
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*" ",0*/

0x00,0x00,0x00,0x7C,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x00,0x00,0x00,/*"!",1*/

0x00,0x04,0x02,0x05,0x02,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*""",2*/

0x90,0xF0,0x9C,0x90,0xD0,0xBC,0x00,0x00,0x03,0x00,0x00,0x03,0x00,0x00,/*"#",3*/

0x00,0x98,0x14,0xFE,0x4C,0x8C,0x00,0x00,0x01,0x02,0x07,0x02,0x01,0x00,/*"$",4*/

0x3C,0x24,0xBC,0x60,0xDC,0x20,0xC0,0x00,0x02,0x01,0x00,0x03,0x02,0x01,/*"%",5*/

0xC0,0x3C,0x64,0x9C,0xE0,0x20,0x00,0x01,0x02,0x02,0x03,0x02,0x02,0x00,/*"&",6*/

0x00,0x07,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*"'",7*/

0x00,0x00,0x00,0xF8,0x04,0x02,0x00,0x00,0x00,0x00,0x00,0x03,0x04,0x00,/*"(",8*/

0x00,0x02,0x04,0xF8,0x00,0x00,0x00,0x00,0x04,0x03,0x00,0x00,0x00,0x00,/*")",9*/

0x00,0x50,0x60,0xF8,0x60,0x50,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,/*"*",10*/

0x00,0x20,0x20,0xF8,0x20,0x20,0x20,0x00,0x00,0x00,0x01,0x00,0x00,0x00,/*"+",11*/

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x02,0x00,0x00,0x00,0x00,/*",",12*/

0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*"-",13*/

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x02,0x00,0x00,0x00,0x00,/*".",14*/

0x00,0x00,0x80,0x60,0x18,0x04,0x00,0x04,0x03,0x00,0x00,0x00,0x00,0x00,/*"/",15*/

0x00,0xF8,0x04,0x04,0x04,0xF8,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"0",16*/

0x00,0x00,0x04,0xFC,0x00,0x00,0x00,0x00,0x00,0x02,0x03,0x02,0x00,0x00,/*"1",17*/

0x00,0x0C,0x84,0x44,0x24,0x1C,0x00,0x00,0x03,0x02,0x02,0x02,0x02,0x00,/*"2",18*/

0x00,0x0C,0x04,0x24,0x24,0xD8,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"3",19*/

0x00,0x60,0x90,0x88,0xFC,0x80,0x00,0x00,0x00,0x00,0x00,0x03,0x02,0x00,/*"4",20*/

0x00,0x3C,0x24,0x14,0x24,0xE4,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"5",21*/

0x00,0xF8,0x24,0x14,0x24,0xE4,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"6",22*/

0x00,0x04,0x04,0xE4,0x14,0x0C,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,/*"7",23*/

0x00,0xDC,0x24,0x24,0x64,0xDC,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"8",24*/

0x00,0x3C,0x44,0x44,0x44,0xF8,0x00,0x00,0x03,0x02,0x02,0x01,0x00,0x00,/*"9",25*/

0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x00,0x00,0x00,/*":",26*/

0x00,0x00,0x00,0x30,0x00,0x00,0x00,0x00,0x00,0x00,0x06,0x00,0x00,0x00,/*";",27*/

0x00,0x20,0x50,0x88,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x02,0x00,/*"<",28*/

0x10,0x50,0x50,0x50,0x50,0x50,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*"=",29*/

0x00,0x00,0x04,0x88,0x50,0x20,0x00,0x00,0x02,0x01,0x00,0x00,0x00,0x00,/*">",30*/

0x00,0x1C,0x04,0xC4,0x24,0x1C,0x00,0x00,0x00,0x00,0x02,0x00,0x00,0x00,/*"?",31*/

0x00,0xF8,0xF4,0x8C,0xFC,0x84,0x78,0x00,0x01,0x02,0x02,0x02,0x02,0x01,/*"@",32*/

0x00,0xC0,0x78,0x4C,0x70,0x80,0x00,0x02,0x03,0x00,0x00,0x00,0x03,0x00,/*"A",33*/

0x00,0xFC,0x24,0x24,0x24,0xDC,0x80,0x00,0x03,0x02,0x02,0x02,0x01,0x00,/*"B",34*/

0x60,0xD8,0x04,0x04,0x04,0x04,0x08,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"C",35*/

0x00,0xFC,0x04,0x04,0x04,0xF8,0x20,0x00,0x03,0x02,0x02,0x02,0x01,0x00,/*"D",36*/

0x00,0xFC,0x24,0x24,0x24,0x04,0x00,0x00,0x03,0x02,0x02,0x02,0x02,0x01,/*"E",37*/

0x00,0xFC,0x24,0x24,0x24,0x04,0x08,0x00,0x03,0x00,0x00,0x00,0x00,0x00,/*"F",38*/

0x60,0x98,0x04,0x04,0x44,0xCC,0x40,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"G",39*/

0x00,0xFC,0x20,0x20,0x20,0xFC,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"H",40*/

0x00,0x00,0x04,0xFC,0x04,0x00,0x00,0x00,0x00,0x02,0x03,0x02,0x00,0x00,/*"I",41*/

0x00,0x00,0x00,0x04,0xFC,0x04,0x00,0x00,0x04,0x04,0x04,0x03,0x00,0x00,/*"J",42*/

0x00,0xFC,0x20,0x70,0x88,0x04,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"K",43*/

0x00,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x02,0x02,0x02,0x02,0x01,/*"L",44*/

0xF0,0x1C,0xE0,0xC0,0x30,0xFC,0x00,0x03,0x00,0x00,0x01,0x00,0x03,0x00,/*"M",45*/

0x00,0xFC,0x18,0x20,0xC0,0xFC,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"N",46*/

0x00,0xF8,0x04,0x04,0x04,0x9C,0x60,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"O",47*/

0x00,0xFC,0x24,0x24,0x24,0x3C,0x00,0x00,0x03,0x00,0x00,0x00,0x00,0x00,/*"P",48*/

0x60,0x9C,0x84,0x84,0x04,0x9C,0x60,0x00,0x01,0x02,0x02,0x03,0x05,0x00,/*"Q",49*/

0x00,0xFC,0x24,0x24,0xE4,0x1C,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"R",50*/

0x00,0x1C,0x24,0x24,0x44,0xCC,0x00,0x00,0x02,0x02,0x02,0x02,0x01,0x00,/*"S",51*/

0x0C,0x04,0x04,0xFC,0x04,0x04,0x08,0x00,0x00,0x00,0x03,0x00,0x00,0x00,/*"T",52*/

0x00,0xFC,0x00,0x00,0x00,0xFC,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"U",53*/

0x00,0x0C,0xF0,0x80,0x70,0x0C,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,/*"V",54*/

0x00,0x7C,0xC0,0x3C,0xC0,0x38,0x04,0x00,0x00,0x01,0x00,0x03,0x00,0x00,/*"W",55*/

0x00,0x04,0x98,0x60,0x98,0x04,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"X",56*/

0x00,0x04,0x38,0xC0,0x30,0x0C,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,/*"Y",57*/

0x00,0x04,0x84,0x64,0x1C,0x04,0x00,0x00,0x03,0x02,0x02,0x02,0x02,0x00,/*"Z",58*/

0x00,0x00,0x00,0xFF,0x01,0x01,0x00,0x00,0x00,0x00,0x07,0x04,0x04,0x00,/*"[",59*/

0x00,0x06,0x18,0x60,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x06,0x00,/*"\",60*/

0x00,0x01,0x01,0xFF,0x00,0x00,0x00,0x00,0x04,0x04,0x07,0x00,0x00,0x00,/*"]",61*/

0x00,0x00,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*"^",62*/

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x08,0x08,0x08,0x08,0x08,0x08,/*"_",63*/

0x00,0x01,0x01,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*"`",64*/

0x00,0xA0,0x50,0x50,0x50,0xE0,0x00,0x00,0x03,0x02,0x02,0x02,0x03,0x02,/*"a",65*/

0x00,0xFC,0x20,0x10,0x10,0xE0,0x00,0x00,0x03,0x02,0x02,0x02,0x01,0x00,/*"b",66*/

0x00,0xE0,0x10,0x10,0x10,0x20,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"c",67*/

0x00,0xE0,0x10,0x10,0x10,0xFC,0x00,0x00,0x01,0x02,0x02,0x02,0x03,0x02,/*"d",68*/

0x00,0xE0,0x90,0x90,0x90,0x60,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"e",69*/

0x00,0x00,0xF8,0x14,0x14,0x04,0x04,0x00,0x00,0x03,0x02,0x00,0x00,0x00,/*"f",70*/

0x00,0x60,0x90,0x90,0xA0,0x50,0x00,0x00,0x07,0x09,0x0A,0x0A,0x06,0x00,/*"g",71*/

0x00,0xFC,0x20,0x10,0x10,0xE0,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"h",72*/

0x00,0x00,0x10,0xF4,0x00,0x00,0x00,0x00,0x00,0x02,0x03,0x02,0x00,0x00,/*"i",73*/

0x00,0x00,0x00,0x00,0x14,0xE4,0x00,0x00,0x04,0x08,0x08,0x04,0x03,0x00,/*"j",74*/

0x00,0xFC,0x80,0x40,0xA0,0x00,0x00,0x00,0x03,0x00,0x00,0x01,0x02,0x00,/*"k",75*/

0x00,0x00,0x02,0xFC,0x00,0x00,0x00,0x00,0x00,0x02,0x03,0x02,0x00,0x00,/*"l",76*/

0x00,0xE0,0x10,0xE0,0x10,0x10,0xE0,0x00,0x03,0x00,0x03,0x00,0x02,0x03,/*"m",77*/

0x00,0xF0,0x20,0x10,0x10,0xE0,0x00,0x00,0x03,0x00,0x00,0x00,0x03,0x00,/*"n",78*/

0x00,0xE0,0x10,0x10,0x10,0xE0,0x00,0x00,0x01,0x02,0x02,0x02,0x01,0x00,/*"o",79*/

0x00,0xE0,0x20,0x10,0x10,0xE0,0x00,0x08,0x0F,0x0A,0x02,0x02,0x01,0x00,/*"p",80*/

0x00,0xE0,0x20,0x20,0x20,0xE0,0x00,0x00,0x01,0x02,0x02,0x0A,0x0F,0x08,/*"q",81*/

0x00,0x10,0xF0,0x20,0x10,0x30,0x00,0x00,0x02,0x03,0x00,0x00,0x00,0x00,/*"r",82*/

0x00,0x20,0x50,0x90,0x90,0x20,0x00,0x00,0x03,0x02,0x02,0x02,0x01,0x00,/*"s",83*/

0x00,0x00,0xF0,0x10,0x00,0x00,0x00,0x00,0x00,0x01,0x02,0x02,0x02,0x00,/*"t",84*/

0x00,0xF0,0x00,0x00,0x00,0xF0,0x00,0x00,0x01,0x02,0x02,0x02,0x03,0x02,/*"u",85*/

0x00,0x20,0xC0,0x00,0xC0,0x20,0x00,0x00,0x00,0x00,0x03,0x00,0x00,0x00,/*"v",86*/

0x00,0xE0,0x80,0x60,0x80,0x60,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x00,/*"w",87*/

0x00,0x00,0x20,0xC0,0x20,0x00,0x00,0x00,0x02,0x01,0x00,0x01,0x02,0x00,/*"x",88*/

0x00,0x20,0xC0,0x00,0xC0,0x20,0x00,0x00,0x00,0x08,0x07,0x00,0x00,0x00,/*"y",89*/

0x00,0x20,0x20,0xE0,0x20,0x00,0x00,0x00,0x02,0x03,0x02,0x02,0x03,0x00,/*"z",90*/

0x00,0x00,0x00,0x20,0xDE,0x01,0x00,0x00,0x00,0x00,0x00,0x07,0x00,0x00,/*"{",91*/

0x00,0x00,0x00,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x00,0x00,/*"|",92*/

0x00,0x01,0xDE,0x20,0x00,0x00,0x00,0x00,0x00,0x07,0x00,0x00,0x00,0x00,/*"}",93*/

0x00,0x01,0x01,0x01,0x02,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*"~",94*/
};
/*宽6像素，高8像素*/
const uint8_t OLED_F6x8[][6] = 
{
	0x00,0x00,0x00,0x00,0x00,0x00,//   0
	0x00,0x00,0x00,0x2F,0x00,0x00,// ! 1
	0x00,0x00,0x07,0x00,0x07,0x00,// " 2
	0x00,0x14,0x7F,0x14,0x7F,0x14,// # 3
	0x00,0x24,0x2A,0x7F,0x2A,0x12,// $ 4
	0x00,0x23,0x13,0x08,0x64,0x62,// % 5
	0x00,0x36,0x49,0x55,0x22,0x50,// & 6
	0x00,0x00,0x00,0x07,0x00,0x00,// ' 7
	0x00,0x00,0x1C,0x22,0x41,0x00,// ( 8
	0x00,0x00,0x41,0x22,0x1C,0x00,// ) 9
	0x00,0x14,0x08,0x3E,0x08,0x14,// * 10
	0x00,0x08,0x08,0x3E,0x08,0x08,// + 11
	0x00,0x00,0x00,0xA0,0x60,0x00,// , 12
	0x00,0x08,0x08,0x08,0x08,0x08,// - 13
	0x00,0x00,0x60,0x60,0x00,0x00,// . 14
	0x00,0x20,0x10,0x08,0x04,0x02,// / 15
	0x00,0x3E,0x51,0x49,0x45,0x3E,// 0 16
	0x00,0x00,0x42,0x7F,0x40,0x00,// 1 17
	0x00,0x42,0x61,0x51,0x49,0x46,// 2 18
	0x00,0x21,0x41,0x45,0x4B,0x31,// 3 19
	0x00,0x18,0x14,0x12,0x7F,0x10,// 4 20
	0x00,0x27,0x45,0x45,0x45,0x39,// 5 21
	0x00,0x3C,0x4A,0x49,0x49,0x30,// 6 22
	0x00,0x01,0x71,0x09,0x05,0x03,// 7 23
	0x00,0x36,0x49,0x49,0x49,0x36,// 8 24
	0x00,0x06,0x49,0x49,0x29,0x1E,// 9 25
	0x00,0x00,0x36,0x36,0x00,0x00,// : 26
	0x00,0x00,0x56,0x36,0x00,0x00,// ; 27
	0x00,0x08,0x14,0x22,0x41,0x00,// < 28
	0x00,0x14,0x14,0x14,0x14,0x14,// = 29
	0x00,0x00,0x41,0x22,0x14,0x08,// > 30
	0x00,0x02,0x01,0x51,0x09,0x06,// ? 31
	0x00,0x3E,0x49,0x55,0x59,0x2E,// @ 32
	0x00,0x7C,0x12,0x11,0x12,0x7C,// A 33
	0x00,0x7F,0x49,0x49,0x49,0x36,// B 34
	0x00,0x3E,0x41,0x41,0x41,0x22,// C 35
	0x00,0x7F,0x41,0x41,0x22,0x1C,// D 36
	0x00,0x7F,0x49,0x49,0x49,0x41,// E 37
	0x00,0x7F,0x09,0x09,0x09,0x01,// F 38
	0x00,0x3E,0x41,0x49,0x49,0x7A,// G 39
	0x00,0x7F,0x08,0x08,0x08,0x7F,// H 40
	0x00,0x00,0x41,0x7F,0x41,0x00,// I 41
	0x00,0x20,0x40,0x41,0x3F,0x01,// J 42
	0x00,0x7F,0x08,0x14,0x22,0x41,// K 43
	0x00,0x7F,0x40,0x40,0x40,0x40,// L 44
	0x00,0x7F,0x02,0x0C,0x02,0x7F,// M 45
	0x00,0x7F,0x04,0x08,0x10,0x7F,// N 46
	0x00,0x3E,0x41,0x41,0x41,0x3E,// O 47
	0x00,0x7F,0x09,0x09,0x09,0x06,// P 48
	0x00,0x3E,0x41,0x51,0x21,0x5E,// Q 49
	0x00,0x7F,0x09,0x19,0x29,0x46,// R 50
	0x00,0x46,0x49,0x49,0x49,0x31,// S 51
	0x00,0x01,0x01,0x7F,0x01,0x01,// T 52
	0x00,0x3F,0x40,0x40,0x40,0x3F,// U 53
	0x00,0x1F,0x20,0x40,0x20,0x1F,// V 54
	0x00,0x3F,0x40,0x38,0x40,0x3F,// W 55
	0x00,0x63,0x14,0x08,0x14,0x63,// X 56
	0x00,0x07,0x08,0x70,0x08,0x07,// Y 57
	0x00,0x61,0x51,0x49,0x45,0x43,// Z 58
	0x00,0x00,0x7F,0x41,0x41,0x00,// [ 59
	0x00,0x02,0x04,0x08,0x10,0x20,// \ 60
	0x00,0x00,0x41,0x41,0x7F,0x00,// ] 61
	0x00,0x04,0x02,0x01,0x02,0x04,// ^ 62
	0x00,0x40,0x40,0x40,0x40,0x40,// _ 63
	0x00,0x00,0x01,0x02,0x04,0x00,// ` 64
	0x00,0x20,0x54,0x54,0x54,0x78,// a 65
	0x00,0x7F,0x48,0x44,0x44,0x38,// b 66
	0x00,0x38,0x44,0x44,0x44,0x20,// c 67
	0x00,0x38,0x44,0x44,0x48,0x7F,// d 68
	0x00,0x38,0x54,0x54,0x54,0x18,// e 69
	0x00,0x08,0x7E,0x09,0x01,0x02,// f 70
	0x00,0x18,0xA4,0xA4,0xA4,0x7C,// g 71
	0x00,0x7F,0x08,0x04,0x04,0x78,// h 72
	0x00,0x00,0x44,0x7D,0x40,0x00,// i 73
	0x00,0x40,0x80,0x84,0x7D,0x00,// j 74
	0x00,0x7F,0x10,0x28,0x44,0x00,// k 75
	0x00,0x00,0x41,0x7F,0x40,0x00,// l 76
	0x00,0x7C,0x04,0x18,0x04,0x78,// m 77
	0x00,0x7C,0x08,0x04,0x04,0x78,// n 78
	0x00,0x38,0x44,0x44,0x44,0x38,// o 79
	0x00,0xFC,0x24,0x24,0x24,0x18,// p 80
	0x00,0x18,0x24,0x24,0x18,0xFC,// q 81
	0x00,0x7C,0x08,0x04,0x04,0x08,// r 82
	0x00,0x48,0x54,0x54,0x54,0x20,// s 83
	0x00,0x04,0x3F,0x44,0x40,0x20,// t 84
	0x00,0x3C,0x40,0x40,0x20,0x7C,// u 85
	0x00,0x1C,0x20,0x40,0x20,0x1C,// v 86
	0x00,0x3C,0x40,0x30,0x40,0x3C,// w 87
	0x00,0x44,0x28,0x10,0x28,0x44,// x 88
	0x00,0x1C,0xA0,0xA0,0xA0,0x7C,// y 89
	0x00,0x44,0x64,0x54,0x4C,0x44,// z 90
	0x00,0x00,0x08,0x7F,0x41,0x00,// { 91
	0x00,0x00,0x00,0x7F,0x00,0x00,// | 92
	0x00,0x00,0x41,0x7F,0x08,0x00,// } 93
	0x00,0x08,0x04,0x08,0x10,0x08,// ~ 94
};
/*********************ASCII字模数据*/


/*汉字字模数据*********************/

/*相同的汉字只需要定义一次，汉字不分先后顺序*/
/*必须全部为汉字或者全角字符，不要加入任何半角字符*/

/*宽16像素，高16像素*/
const ChineseCell16x16_t OLED_CF16x16[] = {
	
	"，",
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x58,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	
	"。",
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x18,0x24,0x24,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	
	"你",
	0x00,0x80,0x60,0xF8,0x07,0x40,0x20,0x18,0x0F,0x08,0xC8,0x08,0x08,0x28,0x18,0x00,
	0x01,0x00,0x00,0xFF,0x00,0x10,0x0C,0x03,0x40,0x80,0x7F,0x00,0x01,0x06,0x18,0x00,
	
	"好",
	0x10,0x10,0xF0,0x1F,0x10,0xF0,0x00,0x80,0x82,0x82,0xE2,0x92,0x8A,0x86,0x80,0x00,
	0x40,0x22,0x15,0x08,0x16,0x61,0x00,0x00,0x40,0x80,0x7F,0x00,0x00,0x00,0x00,0x00,
	
	"世",
	0x20,0x20,0x20,0xFE,0x20,0x20,0xFF,0x20,0x20,0x20,0xFF,0x20,0x20,0x20,0x20,0x00,
	0x00,0x00,0x00,0x7F,0x40,0x40,0x47,0x44,0x44,0x44,0x47,0x40,0x40,0x40,0x00,0x00,
	
	"界",
	0x00,0x00,0x00,0xFE,0x92,0x92,0x92,0xFE,0x92,0x92,0x92,0xFE,0x00,0x00,0x00,0x00,
	0x08,0x08,0x04,0x84,0x62,0x1E,0x01,0x00,0x01,0xFE,0x02,0x04,0x04,0x08,0x08,0x00,
	
	/*按照上面的格式，在这个位置加入新的汉字数据*/
	//...
	"选",
	0x40,0x40,0x42,0xCC,0x00,0x50,0x4E,0xC8,0x48,0x7F,0xC8,0x48,0x48,0x40,0x00,0x00,
	0x00,0x40,0x20,0x1F,0x20,0x50,0x4C,0x43,0x40,0x40,0x4F,0x50,0x50,0x5C,0x40,0x00,
	"项",
	0x08,0x08,0x08,0xF8,0x08,0x08,0x00,0xF2,0x12,0x1A,0xD6,0x12,0x12,0xF2,0x02,0x00,
	0x10,0x30,0x10,0x0F,0x08,0x08,0x80,0x4F,0x20,0x18,0x07,0x10,0x20,0x4F,0x80,0x00,
	"一",
	0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	"二",
	0x00,0x00,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x00,0x00,0x00,
	0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x00,
	"三",
	0x00,0x04,0x84,0x84,0x84,0x84,0x84,0x84,0x84,0x84,0x84,0x84,0x84,0x04,0x00,0x00,
	0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x20,0x00,
	"四",
	0x00,0xFC,0x04,0x04,0x04,0xFC,0x04,0x04,0x04,0xFC,0x04,0x04,0x04,0xFC,0x00,0x00,
	0x00,0x7F,0x28,0x24,0x23,0x20,0x20,0x20,0x20,0x21,0x22,0x22,0x22,0x7F,0x00,0x00,
	"五",
	0x00,0x02,0x42,0x42,0x42,0xC2,0x7E,0x42,0x42,0x42,0x42,0xC2,0x02,0x02,0x00,0x00,
	0x40,0x40,0x40,0x40,0x78,0x47,0x40,0x40,0x40,0x40,0x40,0x7F,0x40,0x40,0x40,0x00,
	"六",
	0x20,0x20,0x20,0x20,0x20,0x20,0x21,0x22,0x2C,0x20,0x20,0x20,0x20,0x20,0x20,0x00,
	0x00,0x40,0x20,0x10,0x0C,0x03,0x00,0x00,0x00,0x01,0x02,0x04,0x18,0x60,0x00,0x00,
	"七",
	0x80,0x80,0x80,0x80,0x80,0x40,0xFF,0x40,0x40,0x40,0x20,0x20,0x20,0x20,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x40,0x40,0x40,0x40,0x40,0x40,0x78,0x00,0x00,
	"八",
	0x00,0x00,0x00,0x00,0x00,0xFC,0x00,0x00,0x00,0x7E,0x80,0x00,0x00,0x00,0x00,0x00,
	0x00,0x80,0x60,0x18,0x07,0x00,0x00,0x00,0x00,0x00,0x03,0x0C,0x30,0x40,0x80,0x00,
	"九",
	0x00,0x10,0x10,0x10,0x10,0xFF,0x10,0x10,0x10,0x10,0xF0,0x00,0x00,0x00,0x00,0x00,
	0x80,0x40,0x20,0x18,0x07,0x00,0x00,0x00,0x00,0x00,0x3F,0x40,0x40,0x40,0x78,0x00,
	"零",
	0x10,0x0C,0x05,0x55,0x55,0x55,0x85,0x7F,0x85,0x55,0x55,0x55,0x05,0x14,0x0C,0x00,
	0x04,0x04,0x02,0x0A,0x09,0x29,0x2A,0x4C,0x48,0xA9,0x19,0x02,0x02,0x04,0x04,0x00,
	"，",
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x58,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	"。",
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x18,0x24,0x24,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	"！",
	0x00,0x00,0x00,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x33,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	"；",
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x5B,0x3B,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	"？",
	0x00,0x00,0x38,0x34,0x02,0x82,0xC2,0x7C,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x37,0x37,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	
	"黑",
	0x00,0x00,0x3E,0x22,0x2A,0x32,0x22,0xFE,0x22,0x32,0x2A,0x22,0x3E,0x00,0x00,0x00,
	0x88,0x68,0x09,0x09,0x29,0xC9,0x09,0x0F,0x29,0xC9,0x09,0x09,0x29,0xC8,0x08,0x00,/*"黑",0*/
	"框",
	0x10,0x10,0xD0,0xFF,0x90,0x10,0x00,0xFE,0x12,0x92,0x92,0xF2,0x92,0x92,0x12,0x00,
	0x04,0x03,0x00,0xFF,0x00,0x03,0x00,0x7F,0x48,0x48,0x48,0x4F,0x48,0x48,0x48,0x00,/*"框",1*/
	"循",
	0x10,0x88,0xC4,0x33,0x00,0xFE,0x12,0xD2,0x52,0x52,0x7F,0x51,0x51,0xD1,0x10,0x00,
	0x01,0x00,0xFF,0x40,0x30,0x0F,0x00,0xFF,0x4A,0x4A,0x4A,0x4A,0x4A,0xFF,0x00,0x00,/*"循",2*/
	"迹",
	0x40,0x40,0x42,0xCC,0x00,0x08,0xC8,0x08,0xF8,0x09,0x0E,0xF8,0x08,0x48,0x88,0x00,
	0x00,0x40,0x20,0x1F,0x20,0x51,0x48,0x46,0x41,0x48,0x50,0x4F,0x40,0x40,0x41,0x00,/*"迹",3*/
	
	"正",
	0x00,0x02,0x02,0xC2,0x02,0x02,0x02,0xFE,0x82,0x82,0x82,0x82,0x82,0x02,0x00,0x00,
	0x40,0x40,0x40,0x7F,0x40,0x40,0x40,0x7F,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,/*"正",0*/
	"在",
	0x08,0x08,0x88,0xC8,0x38,0x0C,0x0B,0x08,0x08,0xE8,0x08,0x08,0x08,0x08,0x08,0x00,
	0x02,0x01,0x00,0xFF,0x40,0x41,0x41,0x41,0x41,0x7F,0x41,0x41,0x41,0x41,0x40,0x00,/*"在",1*/
	"进",
	0x40,0x40,0x42,0xCC,0x00,0x80,0x88,0x88,0xFF,0x88,0x88,0xFF,0x88,0x88,0x80,0x00,
	0x00,0x40,0x20,0x1F,0x20,0x40,0x50,0x4C,0x43,0x40,0x40,0x5F,0x40,0x40,0x40,0x00,/*"进",2*/
	"行",
	0x00,0x10,0x88,0xC4,0x33,0x00,0x40,0x42,0x42,0x42,0xC2,0x42,0x42,0x42,0x40,0x00,
	0x02,0x01,0x00,0xFF,0x00,0x00,0x00,0x00,0x40,0x80,0x7F,0x00,0x00,0x00,0x00,0x00,/*"行",3*/
	"斜",
	0x20,0x10,0x28,0x24,0xE3,0x24,0x28,0x10,0x00,0x22,0xCC,0x00,0xFF,0x00,0x00,0x00,
	0x20,0x11,0x4D,0x81,0x7F,0x01,0x05,0x19,0x00,0x02,0x02,0x02,0xFF,0x01,0x01,0x00,/*"斜",0*/

	
	/*未找到指定汉字时显示的默认图形（一个方框，内部一个问号），请确保其位于数组最末尾*/
	"",		
	0xFF,0x01,0x01,0x01,0x31,0x09,0x09,0x09,0x09,0x89,0x71,0x01,0x01,0x01,0x01,0xFF,
	0xFF,0x80,0x80,0x80,0x80,0x80,0x80,0x96,0x81,0x80,0x80,0x80,0x80,0x80,0x80,0xFF,

};
const ChineseCell12x12_t OLED_CF12x12[] = {
	"你",
	0x20,0x10,0xFC,0x03,0x10,0xCF,0x04,0xF4,0x04,0x54,0x8C,0x00,0x00,0x00,0x0F,0x00,
	0x02,0x01,0x08,0x0F,0x00,0x00,0x03,0x00,/*0*/
	"好",
	0x88,0x78,0x0F,0x88,0x78,0x42,0x42,0xF2,0x4A,0x46,0x40,0x00,0x08,0x05,0x02,0x05,
	0x08,0x00,0x08,0x0F,0x00,0x00,0x00,0x00,/*1*/
	"世",
	0x10,0x10,0xFE,0x10,0x10,0xFF,0x10,0x10,0xFF,0x10,0x10,0x00,0x00,0x00,0x0F,0x08,
	0x08,0x09,0x09,0x09,0x09,0x08,0x08,0x00,/*2*/
	"界",
	0x80,0x9F,0x95,0x55,0x55,0x3F,0x55,0x55,0x95,0x9F,0x80,0x00,0x00,0x08,0x04,0x03,
	0x00,0x00,0x00,0x0F,0x00,0x00,0x00,0x00,/*3*/
	"选",
	0x10,0x11,0xF2,0x00,0x28,0x26,0xE4,0x3F,0xE4,0x24,0x20,0x00,0x08,0x04,0x03,0x04,
	0x0A,0x09,0x08,0x08,0x09,0x0A,0x0B,0x00,/*"选",0*/
	"项",
    0x02,0xFE,0x02,0x00,0xF9,0x09,0x0D,0xEB,0x09,0x09,0xF9,0x00,0x02,0x01,0x01,0x08,
    0x09,0x04,0x02,0x01,0x02,0x04,0x09,0x00,/*"项",1*/
	"一",
	0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*0*/
	"二",
	0x00,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x00,0x00,0x04,0x04,0x04,0x04,
	0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x00,/*"1*/
	"三",
	0x00,0x02,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x02,0x00,0x00,0x08,0x08,0x08,0x08,
	0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x00,/*2*/
	"四",
	0x00,0xFE,0x02,0x82,0x7E,0x02,0x02,0x7E,0x82,0x82,0xFE,0x00,0x00,0x0F,0x05,0x04,
	0x04,0x04,0x04,0x04,0x04,0x04,0x0F,0x00,/*3*/
	"五",
	0x02,0x22,0x22,0xE2,0x3E,0x22,0x22,0x22,0xE2,0x02,0x00,0x00,0x08,0x08,0x0E,0x09,
	0x08,0x08,0x08,0x08,0x0F,0x08,0x08,0x00,/*4*/
	"六",
	0x10,0x10,0x10,0xD0,0x11,0x16,0x10,0x50,0x90,0x10,0x10,0x00,0x08,0x04,0x03,0x00,
	0x00,0x00,0x00,0x00,0x00,0x03,0x0C,0x00,/*5*/
	"七",
	0x20,0x20,0x20,0x20,0xFF,0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,0x00,0x00,
	0x07,0x08,0x08,0x08,0x08,0x08,0x0E,0x00,/*6*/
	"八",
	0x00,0x00,0x00,0xFE,0x00,0x00,0x00,0x7F,0x80,0x00,0x00,0x00,0x08,0x04,0x03,0x00,
	0x00,0x00,0x00,0x00,0x01,0x06,0x08,0x00,/*7*/
	"九",
	0x08,0x08,0x08,0xFF,0x08,0x08,0x08,0xF8,0x00,0x00,0x00,0x00,0x08,0x04,0x03,0x00,
	0x00,0x00,0x00,0x07,0x08,0x08,0x0E,0x00,/*8*/
	"零",
	0x0C,0x25,0xAD,0xAD,0x45,0xBF,0x45,0xAD,0xAD,0x25,0x0C,0x00,0x01,0x01,0x00,0x05,
	0x05,0x05,0x0B,0x09,0x00,0x01,0x01,0x00,/*9*/
	"，",
	0x00,0x00,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x02,0x01,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*0*/
	"。",
	0x00,0x00,0xC0,0x20,0x20,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,
	0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*1*/
	"？",
	0x00,0x0C,0x0E,0xE1,0xF1,0x1E,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x06,
	0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*2*/
	"！",
	0x00,0x00,0x3E,0x3E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x03,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*3*/
	"‘",
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x1A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*4*/
	"’",
	0x00,0x00,0x16,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*5*/
	"“",
	0x00,0x00,0x00,0x00,0x1C,0x1A,0x00,0x1C,0x1A,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*6*/
	"”",
	0x00,0x00,0x16,0x0E,0x00,0x16,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
	0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,/*7*/
	"返",
	0x10,0x11,0xF2,0x00,0xFE,0x0A,0x2A,0x4A,0x89,0x49,0x39,0x00,0x08,0x04,0x03,0x06,
	0x09,0x08,0x0A,0x09,0x08,0x09,0x0A,0x00,/*"返",0*/
	"回",
	0x00,0xFE,0x02,0x02,0xF2,0x92,0x92,0xF2,0x02,0x02,0xFE,0x00,0x00,0x0F,0x04,0x04,
	0x04,0x04,0x04,0x04,0x04,0x04,0x0F,0x00,/*"回",1*/
	"复",
	0x04,0x02,0x7D,0xD5,0x55,0x55,0x55,0x55,0x55,0x7D,0x01,0x00,0x00,0x0A,0x09,0x0B,
	0x05,0x05,0x05,0x05,0x0B,0x09,0x08,0x00,/*0*/
	"位",
	0x10,0xFC,0x03,0x08,0x68,0x88,0x09,0x0A,0x08,0xE8,0x08,0x00,0x00,0x0F,0x00,0x08,
	0x08,0x0B,0x08,0x0C,0x0B,0x08,0x08,0x00,/*1*/
	"与",
	0x00,0x38,0x27,0x24,0x24,0x24,0x24,0x24,0x24,0xE4,0x04,0x00,0x01,0x01,0x01,0x01,
	0x01,0x01,0x09,0x09,0x08,0x07,0x00,0x00,/*2*/
	"循",
	0x24,0xF2,0x09,0x00,0xFE,0x0A,0xEA,0xAA,0xBE,0xA9,0xE9,0x00,0x00,0x0F,0x00,0x08,
	0x07,0x00,0x0F,0x0A,0x0A,0x0A,0x0F,0x00,/*3*/
	"边",
	0x10,0x11,0xF2,0x00,0x08,0x88,0x7F,0x08,0x08,0x08,0xF8,0x00,0x08,0x04,0x03,0x04,
	0x0A,0x09,0x08,0x0A,0x0A,0x0A,0x09,0x00,/*4*/
	"黑",
	0x00,0x5F,0x51,0x55,0x51,0xFF,0x51,0x55,0x51,0x5F,0x00,0x00,0x09,0x05,0x01,0x05,
	0x09,0x01,0x05,0x09,0x01,0x05,0x09,0x00,/*"黑",0*/
	"胶",
	0x00,0xFE,0x92,0xFE,0x40,0x24,0xD5,0x06,0xD4,0x24,0x44,0x00,0x08,0x07,0x08,0x0F,
	0x00,0x08,0x05,0x02,0x05,0x08,0x08,0x00,/*"胶",1*/
	"带",
	0x32,0x92,0x97,0x92,0x92,0xD7,0x92,0x92,0x97,0x92,0x32,0x00,0x00,0x07,0x00,0x00,
	0x00,0x0F,0x00,0x00,0x04,0x07,0x00,0x00,/*"带",2*/
	"迹",
	0x11,0xF2,0x80,0x64,0x04,0xFC,0x05,0x06,0xFC,0x24,0xC4,0x00,0x08,0x07,0x08,0x0A,
	0x09,0x08,0x08,0x0A,0x0B,0x08,0x08,0x00,/*"迹",3*/
	"方",
	0x04,0x04,0x04,0xFC,0x25,0x26,0x24,0x24,0x24,0xE4,0x04,0x00,0x08,0x04,0x03,0x00,
	0x00,0x00,0x08,0x08,0x08,0x07,0x00,0x00,/*0*/
	"框",
	0x88,0x68,0xFF,0x48,0xFE,0x02,0x4A,0x4A,0xFA,0x4A,0x4A,0x00,0x00,0x00,0x0F,0x00,
	0x0F,0x08,0x0A,0x0A,0x0B,0x0A,0x0A,0x00,/*"框",1*/
    "旋",
	0x08,0xF8,0x49,0x4A,0xD0,0x08,0xD7,0x14,0xF4,0x14,0x34,0x00,0x08,0x07,0x08,0x08,
    0x07,0x08,0x07,0x08,0x0F,0x09,0x09,0x00,/*"旋",0*/
    "转",
    0x74,0x4F,0xF4,0x40,0x24,0xE4,0xBC,0xA7,0xA4,0xA4,0x20,0x00,0x02,0x02,0x0F,0x01,
     0x00,0x02,0x02,0x04,0x06,0x09,0x00,0x00,/*"转",1*/
	"斜",
	0x08,0x44,0x4A,0xF9,0x4A,0x44,0x92,0xA4,0x80,0xFF,0x40,0x00,0x02,0x01,0x08,0x0F,
	0x01,0x02,0x00,0x00,0x00,0x0F,0x00,0x00,/*"斜",0*/
	"其",
	0x04,0x04,0xFF,0x54,0x54,0x54,0x54,0x54,0xFF,0x04,0x04,0x00,0x01,0x09,0x05,0x01,
	0x01,0x01,0x01,0x01,0x05,0x09,0x01,0x00,/*0*/
	"他",
	0x20,0x10,0xFC,0x03,0x20,0xFC,0x10,0xFF,0x08,0x84,0xFC,0x00,0x00,0x00,0x0F,0x00,
	0x00,0x07,0x08,0x09,0x08,0x08,0x0E,0x00,/*1*/
	"主",
	0x00,0x88,0x88,0x88,0x89,0xFA,0x88,0x88,0x88,0x88,0x00,0x00,0x08,0x08,0x08,0x08,
	0x08,0x0F,0x08,0x08,0x08,0x08,0x08,0x00,/*"主",0*/
	"页",
	0x01,0xF9,0x09,0x09,0x0D,0xCB,0x09,0x09,0x09,0xF9,0x01,0x00,0x08,0x09,0x04,0x04,
	0x02,0x01,0x02,0x02,0x04,0x05,0x08,0x00,/*"页",1*/
	"校",
	0x88,0x68,0xFF,0x28,0x44,0x24,0xD5,0x06,0xD4,0x24,0x44,0x00,0x00,0x00,0x0F,0x00,
	0x08,0x08,0x05,0x02,0x05,0x08,0x08,0x00,/*"校",0*/
	"准",
	0x04,0x08,0x20,0x10,0xFC,0x27,0x24,0xFD,0x26,0x24,0x04,0x00,0x04,0x02,0x01,0x00,
	0x0F,0x09,0x09,0x0F,0x09,0x09,0x08,0x00,/*"准",1*/
	"增",
	0x10,0x10,0xFF,0x10,0x3E,0xAB,0xA2,0xBE,0xA2,0xAB,0x3E,0x00,0x04,0x04,0x03,0x02,
	0x00,0x0F,0x0A,0x0A,0x0A,0x0F,0x00,0x00,/*"增",0*/
	"加",
	0x08,0x08,0xFF,0x08,0x08,0xF8,0x00,0xFC,0x04,0x04,0xFC,0x00,0x08,0x06,0x01,0x08,
	0x08,0x07,0x00,0x0F,0x04,0x04,0x0F,0x00,/*"加",1*/
	"减",
	0x04,0x08,0xFC,0x04,0xD4,0x54,0xD4,0x04,0xFF,0x84,0x65,0x00,0x02,0x09,0x07,0x00,
	0x03,0x02,0x0B,0x04,0x03,0x05,0x0E,0x00,/*"减",2*/
	"少",
	0x20,0x10,0x0C,0x00,0x00,0xFF,0x00,0x00,0x84,0x08,0x30,0x00,0x08,0x08,0x08,0x04,
	0x04,0x02,0x02,0x01,0x00,0x00,0x00,0x00,/*"少",3*/
	"开",
	0x40,0x42,0x42,0xFE,0x42,0x42,0x42,0xFE,0x42,0x42,0x40,0x00,0x00,0x08,0x06,0x01,
	0x00,0x00,0x00,0x0F,0x00,0x00,0x00,0x00,/*"开",0*/
	"始",
	0x88,0x78,0x0F,0xF8,0x00,0xD8,0x54,0x53,0x50,0xD8,0x30,0x00,0x08,0x05,0x02,0x0D,
	0x00,0x0F,0x04,0x04,0x04,0x0F,0x00,0x00,/*"始",1*/
	"暂",
	0x22,0x2E,0xAB,0xFE,0xAA,0xC0,0xBE,0x8A,0x89,0x79,0x08,0x00,0x00,0x00,0x0F,0x0A,
	0x0A,0x0A,0x0A,0x0A,0x0F,0x00,0x00,0x00,/*"暂",2*/
	"停",
	0x20,0x10,0xFC,0x03,0x82,0xBA,0xAA,0xAB,0xAA,0xBA,0x82,0x00,0x00,0x00,0x0F,0x00,
	0x01,0x02,0x0A,0x0E,0x02,0x02,0x01,0x00,/*"停",3*/
	"后",
	0x00,0x00,0xFE,0x12,0x92,0x92,0x92,0x91,0x91,0x91,0x90,0x00,0x08,0x06,0x01,0x00,
	0x0F,0x04,0x04,0x04,0x04,0x04,0x0F,0x00,/*"后",0*/
	"按",
	0x88,0x88,0xFF,0x48,0x00,0x4C,0xC4,0x75,0x46,0xC4,0x4C,0x00,0x00,0x08,0x0F,0x00,
	0x08,0x08,0x05,0x02,0x02,0x05,0x08,0x00,/*"按",1*/
	"键",
	0x94,0xF3,0x92,0x64,0xDC,0x88,0xAA,0xFF,0xAA,0xBE,0x08,0x00,0x00,0x0F,0x04,0x0A,
	0x07,0x0A,0x0A,0x0F,0x0A,0x0A,0x0A,0x00,/*"键",2*/
	"等",
	0x44,0x53,0x52,0x56,0x52,0x7C,0x53,0xD2,0x56,0x52,0x42,0x00,0x01,0x01,0x03,0x05,
  0x01,0x09,0x09,0x0F,0x01,0x01,0x01,0x00,/*"等",0*/
  "待",
  0x48,0x24,0xF2,0x09,0x50,0x54,0x54,0x5F,0xF4,0x54,0x50,0x00,0x00,0x00,0x0F,0x00,
  0x00,0x01,0x0A,0x08,0x0F,0x00,0x00,0x00,/*"待",1*/
	"成",
	0x00,0xFC,0x24,0x24,0xE4,0x04,0xFF,0x04,0x85,0x66,0x04,0x00,0x08,0x07,0x00,0x02,
	0x0B,0x04,0x02,0x01,0x02,0x04,0x0F,0x00,/*"成",0*/
	"功",
	0x04,0x04,0xFC,0x04,0x04,0x08,0xFF,0x08,0x08,0x08,0xF8,0x00,0x02,0x02,0x01,0x09,
	0x05,0x03,0x00,0x00,0x08,0x08,0x07,0x00,/*"功",1*/
	"连",
	0x21,0xE2,0x00,0x04,0x34,0x2C,0x27,0xF4,0x24,0x24,0x04,0x00,0x08,0x07,0x08,0x09,
	0x09,0x09,0x09,0x0F,0x09,0x09,0x09,0x00,/*"连",2*/
	"接",
	0x88,0x88,0xFF,0x48,0xA4,0xAC,0xB5,0xE6,0xB4,0xAC,0xA4,0x00,0x00,0x08,0x0F,0x00,
	0x08,0x0A,0x0B,0x04,0x04,0x0B,0x08,0x00,/*"接",3*/
	"秒",
	0x12,0xD2,0xFE,0x91,0x40,0x38,0x00,0xFF,0x00,0x04,0xB8,0x00,0x01,0x00,0x0F,0x00,
	0x08,0x08,0x04,0x04,0x02,0x01,0x00,0x00,/*"秒",4*/
	"钟",
	0x88,0x97,0xF4,0x94,0x94,0x00,0xF8,0x88,0xFF,0x88,0xF8,0x00,0x00,0x00,0x0F,0x04,
	0x02,0x00,0x01,0x00,0x0F,0x00,0x01,0x00,/*"钟",5*/
	"自",
	0x00,0xFC,0x24,0x24,0x26,0x25,0x24,0x24,0x24,0xFC,0x00,0x00,0x00,0x0F,0x09,0x09,
	0x09,0x09,0x09,0x09,0x09,0x0F,0x00,0x00,/*"自",6*/
	"动",
	0x10,0xD2,0x32,0x92,0x10,0x00,0x08,0xFF,0x08,0x08,0xF8,0x00,0x03,0x02,0x02,0x02,
	0x03,0x08,0x06,0x01,0x08,0x08,0x07,0x00,/*"动",7*/
	"找",
	0x88,0x88,0xFF,0x48,0x48,0x10,0xFF,0x08,0x89,0x6A,0x08,0x00,0x00,0x08,0x0F,0x00,
	0x08,0x04,0x02,0x01,0x02,0x04,0x0F,0x00,/*"找",8*/
	"顶",
	0x02,0x02,0xFE,0x02,0xF9,0x09,0x0D,0xEB,0x09,0x09,0xF9,0x00,0x00,0x04,0x07,0x08,
	0x09,0x04,0x02,0x01,0x02,0x04,0x09,0x00,/*"顶",9*/
	"点",
	0x00,0xF0,0x10,0x10,0x10,0x1F,0x12,0x12,0x12,0xF2,0x02,0x00,0x08,0x05,0x01,0x05,
	0x09,0x01,0x05,0x09,0x01,0x05,0x08,0x00,/*"点",10*/

    "设",
    0x10,0x11,0xF2,0x00,0x50,0xCF,0x41,0x41,0x4F,0xD0,0x10,0x00,0x00,0x00,0x07,0x02,
    0x08,0x08,0x05,0x02,0x05,0x08,0x08,0x00,/*"设",0*/
    "置",
    0x10,0x17,0xD5,0x55,0x57,0x7D,0x57,0x55,0xD5,0x17,0x10,0x00,0x08,0x08,0x0F,0x08,
    0x08,0x0E,0x08,0x08,0x0F,0x08,0x08,0x00,/*"置",1*/
    "时",
    0xFE,0x22,0x22,0xFE,0x00,0x08,0x48,0x88,0x08,0xFF,0x08,0x00,0x07,0x02,0x02,0x07,
    0x00,0x00,0x00,0x09,0x08,0x0F,0x00,0x00,/*"时",2*/
    "间",
    0x00,0xF9,0x02,0xF8,0x49,0x49,0x49,0x49,0xF9,0x01,0xFF,0x00,0x00,0x0F,0x00,0x03,
    0x02,0x02,0x02,0x02,0x0B,0x08,0x0F,0x00,/*"间",3*/
    "角",
    0x08,0xF4,0x53,0x52,0x52,0xF2,0x5A,0x56,0x50,0xF0,0x00,0x00,0x08,0x07,0x01,0x01,
    0x01,0x07,0x01,0x01,0x09,0x0F,0x00,0x00,/*"角",4*/
    "度",
    0x00,0xFE,0x0A,0x8A,0xBE,0xAA,0xAB,0xAA,0xBE,0x8A,0x0A,0x00,0x08,0x07,0x00,0x08,
    0x09,0x0A,0x04,0x04,0x0A,0x09,0x08,0x00,/*"度",5*/
    "数",
    0x48,0x2A,0x98,0x7F,0x28,0x4A,0x10,0xEF,0x08,0xF8,0x08,0x00,0x09,0x0B,0x05,0x05,
    0x0B,0x00,0x08,0x05,0x02,0x05,0x08,0x00,/*"数",0*/
    "定",
    0x2C,0x24,0xA4,0x24,0x25,0xE6,0x24,0x24,0x24,0x24,0x2C,0x00,0x08,0x04,0x03,0x04,
    0x08,0x0F,0x09,0x09,0x09,0x09,0x08,0x00,/*"定",0*/
    "实",
    0x86,0x82,0xA2,0xCA,0x92,0x83,0xFA,0x82,0x82,0x82,0x86,0x00,0x08,0x08,0x04,0x04,
    0x02,0x01,0x00,0x02,0x02,0x04,0x08,0x00,/*"实",0*/
    "际",
    0xFE,0x02,0x32,0xCE,0x10,0x92,0x12,0xF2,0x12,0x92,0x10,0x00,0x0F,0x02,0x02,0x01,
    0x02,0x01,0x08,0x0F,0x00,0x00,0x03,0x00,/*"际",1*/

	/*未找到指定汉字时显示的默认图形（一个方框，内部一个问号），请确保其位于数组最末尾*/
    "", 
    0xFF,0x01,0x19,0x1D,0x05,0xC3,0xC3,0x45,0x3D,0x19,0x01,0xFF,0x0F,0x08,0x08,0x08,
	0x08,0x0E,0x0E,0x08,0x08,0x08,0x08,0x0F,
};

/*********************汉字字模数据*/


/*图像数据*********************/

/*测试图像（一个方框，内部一个二极管符号），宽16像素，高16像素*/
const uint8_t Diode[] = {
	0xFF,0x01,0x81,0x81,0x81,0xFD,0x89,0x91,0xA1,0xC1,0xFD,0x81,0x81,0x81,0x01,0xFF,
	0xFF,0x80,0x80,0x80,0x80,0x9F,0x88,0x84,0x82,0x81,0x9F,0x80,0x80,0x80,0x80,0xFF,
};

/*测试图像（一个电池符号），宽15像素，高15像素*/
const uint8_t Battery[] = {
	0x00,0xF8,0x08,0xE8,0xE8,0x08,0xE8,0xE8,0x08,0xE8,0xE8,0x08,0xF8,0xC0,0x00,0x00,
	0x1F,0x10,0x17,0x17,0x10,0x17,0x17,0x10,0x17,0x17,0x10,0x1F,0x03,0x00,
};
const uint8_t Win11Wallpaper[] = {
0xFF,0x55,0xFF,0xD5,0xBF,0xF5,0x5F,0xF5,0xAF,0xFD,0xD7,0x7D,0xBF,0xED,0xFB,0xAF,0xFD,0xFB,0xAF,0xFD,0xF7,0xDF,0x7D,0xF7,0xBF,0xED,0xFF,0x7B,0xEF,0xFF,0xFB,0xDF,0xFF,0xFB,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,
0xBF,0xFF,0x5F,0xFF,0x6F,0x3F,0xF7,0x1F,0xB7,0x5F,0x97,0x5F,0x97,0x6F,0x17,0xAF,0x4F,0x1F,0x6F,0x9F,0x3F,0xBB,0x7F,0xDF,0xFB,0xFF,0xDF,0xFD,0x6F,0xFB,0xFF,0xDD,0x77,0xFF,0xDD,0xF7,0xBF,0xED,0x7B,0xDF,0xF5,0x6F,0xFD,0xD7,0xBD,0xF7,0x5D,0xF7,0xDD,0x77,0xDD,0x77,0xDD,0x77,0xDD,0x77,0xDD,0x77,0xDD,0x77,0xDD,0x77,0xDD,0x77,
0xFF,0xAB,0xFE,0xD7,0x7A,0xBF,0xED,0x7B,0xDF,0xF6,0x7B,0xDF,0xFD,0xB7,0xFE,0xEF,0xBB,0xFE,0xEF,0x7B,0xFE,0xBF,0xF7,0xFF,0xDD,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0xFF,0x7F,0xFF,0x7F,0xDF,0xAF,0xFF,0x97,0xFB,0xAF,0xF5,0xD6,0x6B,0xFD,
0xA9,0xFE,0x55,0xFE,0xAB,0x7C,0xCB,0x7D,0xD5,0x9E,0x2A,0x5E,0x25,0xBA,0x47,0xCC,0x13,0x0C,0x33,0x8C,0x72,0x04,0xEA,0x04,0x31,0xC3,0x0F,0x3F,0xFF,0xFF,0xDD,0xFF,0xF7,0xFF,0xDB,0xFE,0x7F,0xF7,0xDD,0xFF,0x77,0xDD,0xFF,0x76,0xBF,0xED,0xFB,0x5E,0xF7,0xBD,0xEB,0x7F,0xD5,0xBF,0xF5,0x5F,0xF5,0xDF,0x75,0xDF,0x75,0xDF,0x75,0xDF,
0xFA,0xAF,0xFE,0xFB,0xAF,0xFD,0xF7,0xDF,0x7D,0xF7,0xFF,0xDD,0xF7,0xFF,0xBE,0xF7,0xFF,0xBD,0xFF,0xF7,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0xBF,0xFF,0x5F,0xAF,0x7F,0x57,0x1F,0x4B,0xB7,0x4B,0x15,0x03,0x35,0x41,0x88,0x1D,0xEA,0x7F,0xB5,0x6F,0x3D,0x56,0x1B,0x0E,0x97,0x06,0x8B,0x85,0x83,0xC9,0x82,
0xC5,0x80,0xC5,0x80,0x05,0x00,0x03,0x00,0x03,0x04,0x03,0x2D,0xD4,0x00,0x01,0x8A,0x55,0x00,0x00,0x00,0x00,0xA9,0x5A,0x05,0x00,0xA2,0x55,0x00,0x00,0xAB,0xFF,0xFF,0xFE,0xFF,0xFF,0xFB,0xFF,0xEF,0xFD,0xBF,0xF7,0xFF,0xDD,0xFF,0x77,0xFD,0xDF,0x77,0xFD,0xDF,0x77,0xFB,0xDE,0xBF,0xED,0x7B,0xDE,0xF7,0x6D,0xFB,0xDF,0xB5,0xFF,0x55,
0xBE,0xFB,0xEF,0xBE,0xFB,0xEF,0xFE,0xBB,0xFF,0xEF,0x7D,0xFF,0xF6,0xFF,0xBF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x5F,0xDF,0x27,0xDB,0x35,0xCA,0x37,0xC5,0x2A,0x45,0x95,0x65,0xBB,0xD1,0xFE,0xD8,0xFD,0xF9,0xDA,0xEE,0x5E,0xE7,0x6F,0xB5,0x66,0x32,0x67,0x42,0x23,0xC5,0x03,0x01,0x03,0x01,0x05,0x01,0x02,0x01,
0x02,0x01,0x02,0x01,0x05,0x03,0xAC,0x50,0x00,0x80,0xA0,0x58,0x57,0x08,0x14,0x03,0x00,0x80,0x20,0x10,0x02,0x00,0x00,0x00,0x08,0x02,0x00,0x80,0xF0,0xFE,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFF,0xFF,0xF7,0xFE,0xFF,0xDB,0xFF,0xBF,0xFD,0xF7,0xDF,0x7D,0xFF,0xF7,0xDA,0x7F,0xEF,0xFB,0x7E,0xD7,0xBD,0xFF,0xEB,0xBE,0xF7,0x5D,
0xAB,0xFE,0xFF,0xDB,0xFF,0x7E,0xF7,0xFF,0xDD,0xFF,0xFF,0xF7,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0x9F,0x5F,0x57,0xA7,0x59,0x07,0xBD,0x97,0xFB,0xAE,0xFF,0x57,0xFE,0x5F,0xB7,0x5F,0x17,0x5D,0x07,0x2D,0x16,0x05,0x0A,0x05,0x04,0x08,0x04,0x08,0x10,0x10,0x20,0xC0,0x00,0x01,0x2F,0xF2,0x0C,0x50,0x00,0x00,0x80,
0x80,0x60,0x30,0x98,0x4C,0x26,0x15,0x09,0x04,0x02,0x01,0x00,0x00,0x08,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x0E,0x0F,0x0F,0x07,0x0F,0x07,0x0F,0x0F,0x0F,0x0F,0x1F,0x1F,0x3F,0x3F,0xFE,0xFF,0xFF,0xDF,0xFD,0xFF,0xEF,0xFD,0xBF,0xF7,0xFF,0xDD,0xFF,0x77,0xFD,0xDF,0xF7,0x7D,0xDF,0xFB,0x6E,0xFF,0xF5,0x5F,
0xBB,0xFF,0xEE,0xFF,0x7B,0xFF,0xEF,0xFF,0xFD,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFE,0xF5,0xDB,0xA5,0x5E,0xAB,0xD5,0x6E,0x73,0x2C,0x77,0x18,0xB7,0x18,0x4D,0x18,0x0D,0x08,0x0D,0x08,0x0C,0x18,0x00,0x18,0x30,0xA0,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0xB7,0x40,0x09,0x06,0x01,0x00,0x88,0x26,0x01,
0x00,0x02,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x0F,0xBE,0xFF,0xFF,0xFB,0xFF,0xBE,0xF7,0xFF,0xDF,0xFD,0x77,0xFF,0xDF,0xFD,0x77,0xFF,0xDD,0x77,0xFF,
0xEF,0xFF,0xFD,0xEF,0xFF,0xFF,0xFB,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0x7F,0xDF,0x7F,0x57,0x9F,0x67,0x17,0x41,0x2D,0x91,0x44,0x11,0x04,0x01,0x00,0x00,0x00,0x00,0x00,0xC0,0x20,0xD0,0xE0,0xC0,0x40,0x80,0x00,0x00,0x00,0xA9,0x55,0xB0,0x40,0x00,0x10,0x04,0xAB,0x55,0xB5,0x40,0x00,0x16,0x40,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x00,0x20,0x10,0x00,0x08,0x00,0x08,0x04,0x02,0x00,0x82,0x01,0x80,0x01,0xC0,0x00,0x40,0x01,0x00,0x01,0x00,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x12,0x00,0xA0,0xFB,0xFF,0xFF,0x7B,0xFF,0xFF,0xEE,0xFF,0xBB,0xFF,0xEF,0xFD,0xBF,0xF7,0xFF,0xDD,0xFF,0x77,
0xEF,0xFE,0x7F,0xEF,0xFF,0x7E,0xFF,0xFF,0xFF,0x7F,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF,0xFD,0xFB,0xF5,0xCA,0x55,0x2A,0x15,0x6A,0x95,0x60,0x8A,0x50,0x00,0x40,0x00,0x00,0x00,0x00,0x00,0x00,0x2A,0x7B,0x06,0x07,0x23,0x1B,0x03,0x0B,0x12,0x04,0x28,0x52,0x09,0xB6,0x41,0x14,0x40,0x00,0x02,0x08,0x23,0x0C,0x50,0x00,0x01,0x44,
0x08,0x20,0x10,0x20,0x40,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x10,0x00,0x08,0x00,0x22,0x01,0x30,0x10,0x08,0x1C,0x02,0x0E,0x00,0x0F,0x04,0x03,0x02,0x03,0x00,0x03,0x01,0x00,0x40,0x00,0x00,0x00,0x80,0xC0,0xE0,0xE0,0xF8,0xFC,0xFE,0xFF,0x7F,0xFF,0xFF,0x7F,0xFF,0xEE,0x7F,0xFF,0xBB,0xFF,0xEE,0x7F,0xFB,0xDF,0x7E,0xF7,0xFF,0x5D,
/* (128 X 64 )*/
};

/*按照上面的格式，在这个位置加入新的图像数据*/
//...

/*********************图像数据*/


/*****************江协科技|版权所有****************/
/*****************jiangxiekeji.com*****************/
