******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Tue Jul 29 02:17:07 2025

OUTPUT FILE NAME:   <Project.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000032c9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000047a0  0001b860  R  X
  SRAM                  20200000   00008000  000006e4  0000791c  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000047a0   000047a0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    000037b0   000037b0    r-x .text
  00003870    00003870    00000f00   00000f00    r-- .rodata
  00004770    00004770    00000030   00000030    r-- .cinit
20200000    20200000    000004e6   00000000    rw-
  20200000    20200000    000004da   00000000    rw- .bss
  202004dc    202004dc    0000000a   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    000037b0     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    000002b8     OLED.o (.text.OLED_ShowImage)
                  00000d48    000002b4     JY61P.o (.text.UART2_IRQHandler)
                  00000ffc    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  0000121c    000001dc            : _printfi.c.obj (.text._pconv_g)
                  000013f8    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000158a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000158c    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000016c8    00000120            : _printfi.c.obj (.text._pconv_e)
                  000017e8    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000018f4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000019f8    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001ae0    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00001bc4    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00001ca0    000000dc     OLED.o (.text.OLED_ClearArea)
                  00001d7c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00001e54    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001f2c    000000d4     main.o (.text.main)
                  00002000    000000c0     motor.o (.text.set_pwm)
                  000020c0    000000b0     OLED.o (.text.OLED_ShowChar)
                  00002170    000000a8     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002218    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000022ba    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000022bc    0000009c     JY61P.o (.text.Serial_JY61P_Zero_Yaw)
                  00002358    0000009a     OLED.o (.text.OLED_Init)
                  000023f2    00000002     --HOLE-- [fill = 0]
                  000023f4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWMA_init)
                  00002480    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000250c    00000082                            : divsf3.S.obj (.text.__divsf3)
                  0000258e    00000002     --HOLE-- [fill = 0]
                  00002590    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000260c    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00002680    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  000026e8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00002750    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000027b6    00000002     --HOLE-- [fill = 0]
                  000027b8    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000281a    00000002     --HOLE-- [fill = 0]
                  0000281c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000287e    00000002     --HOLE-- [fill = 0]
                  00002880    0000005c     main.o (.text.TIMG0_IRQHandler)
                  000028dc    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002938    00000058     OLED.o (.text.OLED_Clear)
                  00002990    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000029e8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00002a40    00000056     OLED.o (.text.OLED_ShowString)
                  00002a96    00000002     --HOLE-- [fill = 0]
                  00002a98    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00002aee    00000054     OLED.o (.text.OLED_I2C_SendByte)
                  00002b42    00000002     --HOLE-- [fill = 0]
                  00002b44    00000054     OLED.o (.text.OLED_Update)
                  00002b98    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00002bea    00000050     OLED.o (.text.OLED_WriteData)
                  00002c3a    00000002     --HOLE-- [fill = 0]
                  00002c3c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00002c88    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_JY61P_init)
                  00002cd4    0000004a     OLED.o (.text.OLED_GPIO_Init)
                  00002d1e    00000002     --HOLE-- [fill = 0]
                  00002d20    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002d6a    00000002     --HOLE-- [fill = 0]
                  00002d6c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00002db4    00000044                 : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002df8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002e38    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00002e78    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00002eb8    0000003e     OLED.o (.text.OLED_Printf)
                  00002ef6    00000002     --HOLE-- [fill = 0]
                  00002ef8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002f34    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002f70    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002fac    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002fe8    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00003024    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000305e    00000002     --HOLE-- [fill = 0]
                  00003060    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000309a    00000036     OLED.o (.text.OLED_SetCursor)
                  000030d0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00003104    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00003138    00000030     OLED.o (.text.OLED_W_SCL)
                  00003168    00000030     OLED.o (.text.OLED_W_SDA)
                  00003198    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  000031c8    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  000031f4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00003220    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  0000324c    0000002a     OLED.o (.text.OLED_WriteCommand)
                  00003276    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000329e    00000002     --HOLE-- [fill = 0]
                  000032a0    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000032c8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000032f0    00000024     OLED.o (.text.OLED_I2C_Start)
                  00003314    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00003338    00000022     Delay.o (.text.Delay_ms)
                  0000335a    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  0000337c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  0000339c    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  000033bc    00000020     driverlib.a : dl_uart.o (.text.DL_UART_transmitDataBlocking)
                  000033dc    00000020     Delay.o (.text.Delay_us)
                  000033fc    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000341a    00000002     --HOLE-- [fill = 0]
                  0000341c    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000343a    00000002     --HOLE-- [fill = 0]
                  0000343c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00003458    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00003474    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00003490    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000034ac    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000034c8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000034e4    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00003500    0000001c     OLED.o (.text.OLED_I2C_Stop)
                  0000351c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00003534    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  0000354c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00003564    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000357c    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00003594    00000018     motor.o (.text.DL_GPIO_setPins)
                  000035ac    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000035c4    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000035dc    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000035f4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  0000360c    00000018     main.o (.text.DL_Timer_startCounter)
                  00003624    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000363c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00003654    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  0000366c    00000016     main.o (.text.DL_GPIO_readPins)
                  00003682    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00003698    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  000036ac    00000014     motor.o (.text.DL_GPIO_clearPins)
                  000036c0    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  000036d4    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000036e8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000036fc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00003710    00000014     JY61P.o (.text.DL_UART_receiveData)
                  00003724    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00003738    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  0000374c    00000012     main.o (.text.DL_Timer_getPendingInterrupt)
                  0000375e    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00003770    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00003782    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00003794    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000037a4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000037b4    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  000037c4    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  000037d4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000037e2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000037f0    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000037fe    00000002     --HOLE-- [fill = 0]
                  00003800    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  0000380c    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00003816    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003820    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003830    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000383a    0000000a            : vsprintf.c.obj (.text._outc)
                  00003844    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000384c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00003854    00000006     libc.a : exit.c.obj (.text:abort)
                  0000385a    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  0000385e    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00003862    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00003866    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  0000386a    00000006     --HOLE-- [fill = 0]

.cinit     0    00004770    00000030     
                  00004770    0000000c     (__TI_handler_table)
                  0000477c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004784    00000007     (.cinit..data.load) [load image, compression = lzss]
                  0000478b    00000001     --HOLE-- [fill = 0]
                  0000478c    00000010     (__TI_cinit_table)
                  0000479c    00000004     --HOLE-- [fill = 0]

.rodata    0    00003870    00000f00     
                  00003870    000005f0     OLED_DATA.o (.rodata.OLED_F8x16)
                  00003e60    00000532     OLED_DATA.o (.rodata.OLED_F7x12)
                  00004392    0000023a     OLED_DATA.o (.rodata.OLED_F6x8)
                  000045cc    00000003     ti_msp_dl_config.o (.rodata.gPWMAClockConfig)
                  000045cf    00000001     --HOLE-- [fill = 0]
                  000045d0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000046d1    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000046d4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000046fc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00004710    00000011     libc.a : _printfi.c.obj (.rodata.str1.10348868589481759720.1)
                  00004721    00000011            : _printfi.c.obj (.rodata.str1.15363888844622738466.1)
                  00004732    0000000c     main.o (.rodata.str1.8154729771448623357.1)
                  0000473e    0000000b     main.o (.rodata.str1.15159059442110792349.1)
                  00004749    00000001     --HOLE-- [fill = 0]
                  0000474a    0000000a     ti_msp_dl_config.o (.rodata.gUART_JY61PConfig)
                  00004754    0000000a     main.o (.rodata.str1.17100691992556644108.1)
                  0000475e    00000002     ti_msp_dl_config.o (.rodata.gUART_JY61PClockConfig)
                  00004760    00000008     ti_msp_dl_config.o (.rodata.gPWMAConfig)
                  00004768    00000008     main.o (.rodata.str1.18227636981041470289.1)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000004da     UNINITIALIZED
                  20200000    00000400     (.common:OLED_DisplayBuf)
                  20200400    000000bc     (.common:gPWMABackup)
                  202004bc    00000009     (.common:receivedData)
                  202004c5    00000001     (.common:PitchH)
                  202004c6    00000001     (.common:PitchL)
                  202004c7    00000001     (.common:RollH)
                  202004c8    00000004     (.common:Pitch)
                  202004cc    00000004     (.common:Roll)
                  202004d0    00000004     (.common:Yaw)
                  202004d4    00000001     (.common:RollL)
                  202004d5    00000001     (.common:SUM)
                  202004d6    00000001     (.common:VH)
                  202004d7    00000001     (.common:VL)
                  202004d8    00000001     (.common:YawH)
                  202004d9    00000001     (.common:YawL)

.data      0    202004dc    0000000a     UNINITIALIZED
                  202004dc    00000004     OLED.o (.data.FPS)
                  202004e0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004e4    00000001     JY61P.o (.data.RxState)
                  202004e5    00000001     JY61P.o (.data.dataIndex)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             1436    86        188    
       main.o                         412     41        0      
       motor.o                        236     0         0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       Delay.o                        66      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2158    319       188    
                                                               
    .\OLED\
       OLED_DATA.o                    0       3420      0      
       OLED.o                         2104    0         1028   
    +--+------------------------------+-------+---------+---------+
       Total:                         2104    3420      1028   
                                                               
    .\USART_JY61P\
       JY61P.o                        868     0         32     
    +--+------------------------------+-------+---------+---------+
       Total:                         868     0         32     
                                                               
    C:/TI/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_uart.o                      122     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1008    0         0      
                                                               
    D:\apps\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5406    291       4      
                                                               
    D:\apps\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\apps\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2670    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       43        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   14218   4073      1764   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000478c records: 2, size/record: 8, table size: 16
	.bss: load addr=0000477c, load size=00000008 bytes, run addr=20200000, run size=000004da bytes, compression=zero_init
	.data: load addr=00004784, load size=00000007 bytes, run addr=202004dc, run size=0000000a bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004770 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000013f9     00003820     0000381e   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000385b  ADC0_IRQHandler                      
0000385b  ADC1_IRQHandler                      
0000385b  AES_IRQHandler                       
0000385e  C$$EXIT                              
0000385b  CANFD0_IRQHandler                    
0000385b  DAC0_IRQHandler                      
0000380d  DL_Common_delayCycles                
00001bc5  DL_SYSCTL_configSYSPLL               
00002db5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000018f5  DL_Timer_initFourCCPWMMode           
000019f9  DL_Timer_initTimerMode               
000034ad  DL_Timer_setCaptCompUpdateMethod     
000035f5  DL_Timer_setCaptureCompareOutCtl     
000037a5  DL_Timer_setCaptureCompareValue      
000034c9  DL_Timer_setClockConfig              
00002d6d  DL_UART_init                         
0000375f  DL_UART_setClockConfig               
000033bd  DL_UART_transmitDataBlocking         
0000385b  DMA_IRQHandler                       
0000385b  Default_Handler                      
00003339  Delay_ms                             
000033dd  Delay_us                             
202004dc  FPS                                  
0000385b  GROUP0_IRQHandler                    
0000385b  GROUP1_IRQHandler                    
0000385f  HOSTexit                             
0000385b  HardFault_Handler                    
0000385b  I2C0_IRQHandler                      
0000385b  I2C1_IRQHandler                      
0000385b  NMI_Handler                          
00002939  OLED_Clear                           
00001ca1  OLED_ClearArea                       
20200000  OLED_DisplayBuf                      
00004392  OLED_F6x8                            
00003e60  OLED_F7x12                           
00003870  OLED_F8x16                           
00002cd5  OLED_GPIO_Init                       
00002aef  OLED_I2C_SendByte                    
000032f1  OLED_I2C_Start                       
00003501  OLED_I2C_Stop                        
00002359  OLED_Init                            
00002eb9  OLED_Printf                          
0000309b  OLED_SetCursor                       
000020c1  OLED_ShowChar                        
00000a91  OLED_ShowImage                       
00002a41  OLED_ShowString                      
00002b45  OLED_Update                          
00003139  OLED_W_SCL                           
00003169  OLED_W_SDA                           
0000324d  OLED_WriteCommand                    
00002beb  OLED_WriteData                       
0000385b  PendSV_Handler                       
202004c8  Pitch                                
202004c5  PitchH                               
202004c6  PitchL                               
0000385b  RTC_IRQHandler                       
00003863  Reset_Handler                        
202004cc  Roll                                 
202004c7  RollH                                
202004d4  RollL                                
202004e4  RxState                              
0000385b  SPI0_IRQHandler                      
0000385b  SPI1_IRQHandler                      
202004d5  SUM                                  
0000385b  SVC_Handler                          
00002171  SYSCFG_DL_GPIO_init                  
000023f5  SYSCFG_DL_PWMA_init                  
00002f35  SYSCFG_DL_SYSCTL_init                
00003105  SYSCFG_DL_TIMER_0_init               
00002c89  SYSCFG_DL_UART_JY61P_init            
000032a1  SYSCFG_DL_init                       
00002681  SYSCFG_DL_initPower                  
000022bd  Serial_JY61P_Zero_Yaw                
0000385b  SysTick_Handler                      
0000385b  TIMA0_IRQHandler                     
0000385b  TIMA1_IRQHandler                     
00002881  TIMG0_IRQHandler                     
0000385b  TIMG12_IRQHandler                    
0000385b  TIMG6_IRQHandler                     
0000385b  TIMG7_IRQHandler                     
0000385b  TIMG8_IRQHandler                     
00003771  TI_memcpy_small                      
000037f1  TI_memset_small                      
0000385b  UART0_IRQHandler                     
0000385b  UART1_IRQHandler                     
00000d49  UART2_IRQHandler                     
0000385b  UART3_IRQHandler                     
202004d6  VH                                   
202004d7  VL                                   
202004d0  Yaw                                  
202004d8  YawH                                 
202004d9  YawL                                 
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
0000478c  __TI_CINIT_Base                      
0000479c  __TI_CINIT_Limit                     
0000479c  __TI_CINIT_Warm                      
00004770  __TI_Handler_Table_Base              
0000477c  __TI_Handler_Table_Limit             
00002fe9  __TI_auto_init_nobinit_nopinit       
00002591  __TI_decompress_lzss                 
00003783  __TI_decompress_none                 
00002991  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
000037c5  __TI_zero_init                       
00001403  __adddf3                             
00001e5f  __addsf3                             
000045d0  __aeabi_ctype_table_                 
000045d0  __aeabi_ctype_table_C                
00002d21  __aeabi_d2iz                         
00001403  __aeabi_dadd                         
000027b9  __aeabi_dcmpeq                       
000027f5  __aeabi_dcmpge                       
00002809  __aeabi_dcmpgt                       
000027e1  __aeabi_dcmple                       
000027cd  __aeabi_dcmplt                       
000017e9  __aeabi_ddiv                         
00001ae1  __aeabi_dmul                         
000013f9  __aeabi_dsub                         
202004e0  __aeabi_errno                        
00003845  __aeabi_errno_addr                   
00002e39  __aeabi_f2d                          
00001e5f  __aeabi_fadd                         
0000281d  __aeabi_fcmpeq                       
00002859  __aeabi_fcmpge                       
0000286d  __aeabi_fcmpgt                       
00002845  __aeabi_fcmple                       
00002831  __aeabi_fcmplt                       
0000250d  __aeabi_fdiv                         
00002481  __aeabi_fmul                         
00001e55  __aeabi_fsub                         
000031f5  __aeabi_i2d                          
00002f71  __aeabi_i2f                          
00002a99  __aeabi_idiv                         
0000158b  __aeabi_idiv0                        
00002a99  __aeabi_idivmod                      
000022bb  __aeabi_ldiv0                        
0000341d  __aeabi_llsl                         
00003315  __aeabi_lmul                         
00003801  __aeabi_memclr                       
00003801  __aeabi_memclr4                      
00003801  __aeabi_memclr8                      
0000384d  __aeabi_memcpy                       
0000384d  __aeabi_memcpy4                      
0000384d  __aeabi_memcpy8                      
000037d5  __aeabi_memset                       
000037d5  __aeabi_memset4                      
000037d5  __aeabi_memset8                      
00002df9  __aeabi_uidiv                        
00002df9  __aeabi_uidivmod                     
00003725  __aeabi_uldivmod                     
0000341d  __ashldi3                            
ffffffff  __binit__                            
000026e9  __cmpdf2                             
00003025  __cmpsf2                             
000017e9  __divdf3                             
0000250d  __divsf3                             
000026e9  __eqdf2                              
00003025  __eqsf2                              
00002e39  __extendsfdf2                        
00002d21  __fixdfsi                            
000031f5  __floatsidf                          
00002f71  __floatsisf                          
0000260d  __gedf2                              
00002fad  __gesf2                              
0000260d  __gtdf2                              
00002fad  __gtsf2                              
000026e9  __ledf2                              
00003025  __lesf2                              
000026e9  __ltdf2                              
00003025  __ltsf2                              
UNDEFED   __mpu_init                           
00001ae1  __muldf3                             
00003315  __muldi3                             
00003061  __muldsi3                            
00002481  __mulsf3                             
000026e9  __nedf2                              
00003025  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000013f9  __subdf3                             
00001e55  __subsf3                             
00002219  __udivmoddi4                         
000032c9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00003867  _system_pre_init                     
00003855  abort                                
00002e79  atoi                                 
ffffffff  binit                                
202004e5  dataIndex                            
000028dd  frexp                                
000028dd  frexpl                               
20200400  gPWMABackup                          
00000000  interruptVectors                     
00001d7d  ldexp                                
00001d7d  ldexpl                               
00001f2d  main                                 
0000335b  memccpy                              
202004bc  receivedData                         
00001d7d  scalbn                               
00001d7d  scalbnl                              
00002001  set_pwm                              
00003221  vsprintf                             
000037b5  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  OLED_ShowImage                       
00000d49  UART2_IRQHandler                     
000013f9  __aeabi_dsub                         
000013f9  __subdf3                             
00001403  __adddf3                             
00001403  __aeabi_dadd                         
0000158b  __aeabi_idiv0                        
000017e9  __aeabi_ddiv                         
000017e9  __divdf3                             
000018f5  DL_Timer_initFourCCPWMMode           
000019f9  DL_Timer_initTimerMode               
00001ae1  __aeabi_dmul                         
00001ae1  __muldf3                             
00001bc5  DL_SYSCTL_configSYSPLL               
00001ca1  OLED_ClearArea                       
00001d7d  ldexp                                
00001d7d  ldexpl                               
00001d7d  scalbn                               
00001d7d  scalbnl                              
00001e55  __aeabi_fsub                         
00001e55  __subsf3                             
00001e5f  __addsf3                             
00001e5f  __aeabi_fadd                         
00001f2d  main                                 
00002001  set_pwm                              
000020c1  OLED_ShowChar                        
00002171  SYSCFG_DL_GPIO_init                  
00002219  __udivmoddi4                         
000022bb  __aeabi_ldiv0                        
000022bd  Serial_JY61P_Zero_Yaw                
00002359  OLED_Init                            
000023f5  SYSCFG_DL_PWMA_init                  
00002481  __aeabi_fmul                         
00002481  __mulsf3                             
0000250d  __aeabi_fdiv                         
0000250d  __divsf3                             
00002591  __TI_decompress_lzss                 
0000260d  __gedf2                              
0000260d  __gtdf2                              
00002681  SYSCFG_DL_initPower                  
000026e9  __cmpdf2                             
000026e9  __eqdf2                              
000026e9  __ledf2                              
000026e9  __ltdf2                              
000026e9  __nedf2                              
000027b9  __aeabi_dcmpeq                       
000027cd  __aeabi_dcmplt                       
000027e1  __aeabi_dcmple                       
000027f5  __aeabi_dcmpge                       
00002809  __aeabi_dcmpgt                       
0000281d  __aeabi_fcmpeq                       
00002831  __aeabi_fcmplt                       
00002845  __aeabi_fcmple                       
00002859  __aeabi_fcmpge                       
0000286d  __aeabi_fcmpgt                       
00002881  TIMG0_IRQHandler                     
000028dd  frexp                                
000028dd  frexpl                               
00002939  OLED_Clear                           
00002991  __TI_ltoa                            
00002a41  OLED_ShowString                      
00002a99  __aeabi_idiv                         
00002a99  __aeabi_idivmod                      
00002aef  OLED_I2C_SendByte                    
00002b45  OLED_Update                          
00002beb  OLED_WriteData                       
00002c89  SYSCFG_DL_UART_JY61P_init            
00002cd5  OLED_GPIO_Init                       
00002d21  __aeabi_d2iz                         
00002d21  __fixdfsi                            
00002d6d  DL_UART_init                         
00002db5  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002df9  __aeabi_uidiv                        
00002df9  __aeabi_uidivmod                     
00002e39  __aeabi_f2d                          
00002e39  __extendsfdf2                        
00002e79  atoi                                 
00002eb9  OLED_Printf                          
00002f35  SYSCFG_DL_SYSCTL_init                
00002f71  __aeabi_i2f                          
00002f71  __floatsisf                          
00002fad  __gesf2                              
00002fad  __gtsf2                              
00002fe9  __TI_auto_init_nobinit_nopinit       
00003025  __cmpsf2                             
00003025  __eqsf2                              
00003025  __lesf2                              
00003025  __ltsf2                              
00003025  __nesf2                              
00003061  __muldsi3                            
0000309b  OLED_SetCursor                       
00003105  SYSCFG_DL_TIMER_0_init               
00003139  OLED_W_SCL                           
00003169  OLED_W_SDA                           
000031f5  __aeabi_i2d                          
000031f5  __floatsidf                          
00003221  vsprintf                             
0000324d  OLED_WriteCommand                    
000032a1  SYSCFG_DL_init                       
000032c9  _c_int00_noargs                      
000032f1  OLED_I2C_Start                       
00003315  __aeabi_lmul                         
00003315  __muldi3                             
00003339  Delay_ms                             
0000335b  memccpy                              
000033bd  DL_UART_transmitDataBlocking         
000033dd  Delay_us                             
0000341d  __aeabi_llsl                         
0000341d  __ashldi3                            
000034ad  DL_Timer_setCaptCompUpdateMethod     
000034c9  DL_Timer_setClockConfig              
00003501  OLED_I2C_Stop                        
000035f5  DL_Timer_setCaptureCompareOutCtl     
00003725  __aeabi_uldivmod                     
0000375f  DL_UART_setClockConfig               
00003771  TI_memcpy_small                      
00003783  __TI_decompress_none                 
000037a5  DL_Timer_setCaptureCompareValue      
000037b5  wcslen                               
000037c5  __TI_zero_init                       
000037d5  __aeabi_memset                       
000037d5  __aeabi_memset4                      
000037d5  __aeabi_memset8                      
000037f1  TI_memset_small                      
00003801  __aeabi_memclr                       
00003801  __aeabi_memclr4                      
00003801  __aeabi_memclr8                      
0000380d  DL_Common_delayCycles                
00003845  __aeabi_errno_addr                   
0000384d  __aeabi_memcpy                       
0000384d  __aeabi_memcpy4                      
0000384d  __aeabi_memcpy8                      
00003855  abort                                
0000385b  ADC0_IRQHandler                      
0000385b  ADC1_IRQHandler                      
0000385b  AES_IRQHandler                       
0000385b  CANFD0_IRQHandler                    
0000385b  DAC0_IRQHandler                      
0000385b  DMA_IRQHandler                       
0000385b  Default_Handler                      
0000385b  GROUP0_IRQHandler                    
0000385b  GROUP1_IRQHandler                    
0000385b  HardFault_Handler                    
0000385b  I2C0_IRQHandler                      
0000385b  I2C1_IRQHandler                      
0000385b  NMI_Handler                          
0000385b  PendSV_Handler                       
0000385b  RTC_IRQHandler                       
0000385b  SPI0_IRQHandler                      
0000385b  SPI1_IRQHandler                      
0000385b  SVC_Handler                          
0000385b  SysTick_Handler                      
0000385b  TIMA0_IRQHandler                     
0000385b  TIMA1_IRQHandler                     
0000385b  TIMG12_IRQHandler                    
0000385b  TIMG6_IRQHandler                     
0000385b  TIMG7_IRQHandler                     
0000385b  TIMG8_IRQHandler                     
0000385b  UART0_IRQHandler                     
0000385b  UART1_IRQHandler                     
0000385b  UART3_IRQHandler                     
0000385e  C$$EXIT                              
0000385f  HOSTexit                             
00003863  Reset_Handler                        
00003867  _system_pre_init                     
00003870  OLED_F8x16                           
00003e60  OLED_F7x12                           
00004392  OLED_F6x8                            
000045d0  __aeabi_ctype_table_                 
000045d0  __aeabi_ctype_table_C                
00004770  __TI_Handler_Table_Base              
0000477c  __TI_Handler_Table_Limit             
0000478c  __TI_CINIT_Base                      
0000479c  __TI_CINIT_Limit                     
0000479c  __TI_CINIT_Warm                      
20200000  OLED_DisplayBuf                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200400  gPWMABackup                          
202004bc  receivedData                         
202004c5  PitchH                               
202004c6  PitchL                               
202004c7  RollH                                
202004c8  Pitch                                
202004cc  Roll                                 
202004d0  Yaw                                  
202004d4  RollL                                
202004d5  SUM                                  
202004d6  VH                                   
202004d7  VL                                   
202004d8  YawH                                 
202004d9  YawL                                 
202004dc  FPS                                  
202004e0  __aeabi_errno                        
202004e4  RxState                              
202004e5  dataIndex                            
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[220 symbols]
