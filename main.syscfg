/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4110"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12  = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121 = ADC12.addInstance();
const GPIO   = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1  = GPIO.addInstance();
const GPIO2  = GPIO.addInstance();
const GPIO3  = GPIO.addInstance();
const GPIO4  = GPIO.addInstance();
const PWM    = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1   = PWM.addInstance();
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER  = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1 = TIMER.addInstance();
const TIMER2 = TIMER.addInstance();
const UART   = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1  = UART.addInstance();
const UART2  = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
const divider7       = system.clockTree["PLL_PDIV"];
divider7.divideValue = 8;

const divider9       = system.clockTree["UDIV"];
divider9.divideValue = 2;

const multiplier2         = system.clockTree["PLL_QDIV"];
multiplier2.multiplyValue = 40;

const mux8       = system.clockTree["HSCLKMUX"];
mux8.inputSelect = "HSCLKMUX_SYSPLL0";

ADC121.$name                             = "ADC1";
ADC121.sampClkDiv                        = "DL_ADC12_CLOCK_DIVIDE_8";
ADC121.adcMem0_name                      = "ADC_Channel0";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.adcPin0Config.hideOutputInversion = scripting.forceWrite(false);
ADC121.adcPin0Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric6";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                          = "GPIO_OLED";
GPIO1.associatedPins.create(2);
GPIO1.associatedPins[0].$name        = "PIN_SCL";
GPIO1.associatedPins[0].assignedPort = "PORTA";
GPIO1.associatedPins[0].assignedPin  = "1";
GPIO1.associatedPins[0].pin.$assign  = "PA1";
GPIO1.associatedPins[1].$name        = "PIN_SDA";
GPIO1.associatedPins[1].assignedPort = "PORTA";
GPIO1.associatedPins[1].assignedPin  = "0";
GPIO1.associatedPins[1].pin.$assign  = "PA0";

GPIO2.$name                              = "GPIO_Key";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].direction        = "INPUT";
GPIO2.associatedPins[0].internalResistor = "PULL_UP";
GPIO2.associatedPins[0].$name            = "K1";
GPIO2.associatedPins[0].pin.$assign      = "PB19";
GPIO2.associatedPins[1].$name            = "K2";
GPIO2.associatedPins[1].direction        = "INPUT";
GPIO2.associatedPins[1].internalResistor = "PULL_UP";
GPIO2.associatedPins[1].pin.$assign      = "PA16";

GPIO3.$name                         = "TB6612";
GPIO3.port                          = "PORTB";
GPIO3.associatedPins.create(4);
GPIO3.associatedPins[0].$name       = "AIN1";
GPIO3.associatedPins[0].assignedPin = "9";
GPIO3.associatedPins[0].pin.$assign = "PB9";
GPIO3.associatedPins[1].$name       = "AIN2";
GPIO3.associatedPins[1].assignedPin = "10";
GPIO3.associatedPins[1].pin.$assign = "PB10";
GPIO3.associatedPins[2].$name       = "BIN1";
GPIO3.associatedPins[2].assignedPin = "7";
GPIO3.associatedPins[2].pin.$assign = "PB7";
GPIO3.associatedPins[3].$name       = "BIN2";
GPIO3.associatedPins[3].assignedPin = "6";
GPIO3.associatedPins[3].pin.$assign = "PB6";

GPIO4.$name                         = "Gray_Address";
GPIO4.associatedPins.create(3);
GPIO4.associatedPins[0].$name       = "PIN_0";
GPIO4.associatedPins[0].pin.$assign = "PB0";
GPIO4.associatedPins[1].$name       = "PIN_1";
GPIO4.associatedPins[1].pin.$assign = "PB1";
GPIO4.associatedPins[2].$name       = "PIN_2";
GPIO4.associatedPins[2].pin.$assign = "PB2";

PWM1.$name                              = "PWMA";
PWM1.clockPrescale                      = 40;
PWM1.timerCount                         = 100;
PWM1.timerStartTimer                    = true;
PWM1.pwmMode                            = "EDGE_ALIGN_UP";
PWM1.peripheral.$assign                 = "TIMA0";
PWM1.peripheral.ccp0Pin.$assign         = "PB14";
PWM1.peripheral.ccp1Pin.$assign         = "PA7";
PWM1.PWM_CHANNEL_0.$name                = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_1.$name                = "ti_driverlib_pwm_PWMTimerCC1";
PWM1.ccp0PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp0PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp0PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp0PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp0PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric2";
PWM1.ccp1PinConfig.direction            = scripting.forceWrite("OUTPUT");
PWM1.ccp1PinConfig.hideOutputInversion  = scripting.forceWrite(false);
PWM1.ccp1PinConfig.onlyInternalResistor = scripting.forceWrite(false);
PWM1.ccp1PinConfig.passedPeripheralType = scripting.forceWrite("Digital");
PWM1.ccp1PinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric3";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.clockTreeEn           = true;

TIMER1.$name              = "TIMER_0";
TIMER1.timerClkDiv        = 8;
TIMER1.timerClkPrescale   = 100;
TIMER1.interrupts         = ["ZERO"];
TIMER1.timerMode          = "PERIODIC";
TIMER1.timerPeriod        = "10ms";
TIMER1.peripheral.$assign = "TIMG0";

TIMER2.$name              = "TIMER_control";
TIMER2.timerClkDiv        = 8;
TIMER2.timerStartTimer    = true;
TIMER2.timerMode          = "PERIODIC";
TIMER2.timerClkPrescale   = 100;
TIMER2.interrupts         = ["ZERO"];
TIMER2.timerPeriod        = "1ms";
TIMER2.peripheral.$assign = "TIMG6";

UART1.$name                            = "UART_JY61P";
UART1.enabledInterrupts                = ["RX"];
UART1.peripheral.$assign               = "UART2";
UART1.peripheral.rxPin.$assign         = "PB16";
UART1.peripheral.txPin.$assign         = "PB17";
UART1.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART1.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART1.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART1.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART1.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric1";

UART2.$name                            = "UART_0";
UART2.targetBaudRate                   = 115200;
UART2.txPinConfig.direction            = scripting.forceWrite("OUTPUT");
UART2.txPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.txPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.txPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.txPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric4";
UART2.rxPinConfig.hideOutputInversion  = scripting.forceWrite(false);
UART2.rxPinConfig.onlyInternalResistor = scripting.forceWrite(false);
UART2.rxPinConfig.passedPeripheralType = scripting.forceWrite("Digital");
UART2.rxPinConfig.$name                = "ti_driverlib_gpio_GPIOPinGeneric5";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.$suggestSolution         = "ADC1";
ADC121.peripheral.adcPin0.$suggestSolution = "PA15";
Board.peripheral.$suggestSolution          = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution = "PA20";
Board.peripheral.swdioPin.$suggestSolution = "PA19";
UART2.peripheral.$suggestSolution          = "UART0";
UART2.peripheral.rxPin.$suggestSolution    = "PA11";
UART2.peripheral.txPin.$suggestSolution    = "PA10";
