#include "ti_msp_dl_config.h"
#include "uart_my.h"
#include "delay.h"

void send_char(UART_Regs *uart, uint8_t data){

    DL_UART_transmitDataCheck(uart,  data);
}
void send_str(UART_Regs *uart, uint8_t *data){

    while (*data != 0 && data != 0)
    {
        send_char(uart, *data);
        data ++;
    }
}

void SendCmd(UART_Regs *uart, uint8_t *cmd, uint8_t len) {
    for (uint8_t i = 0; i < len; i++) {
delay_cycles(3200);
        send_char(uart, cmd[i]);
    }
}