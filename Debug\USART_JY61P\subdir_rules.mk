################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
USART_JY61P/%.o: ../USART_JY61P/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccstheia141/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy" -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy/USART_JY61P" -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy/MOTOR" -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Trailing" -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy/KEY" -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy/OLED" -I"C:/Users/<USER>/workspace_ccstheia/new_24H copy/Debug" -I"C:/ti/mspm0_sdk_2_01_00_03/source/third_party/CMSIS/Core/Include" -I"C:/ti/mspm0_sdk_2_01_00_03/source" -gdwarf-3 -MMD -MP -MF"USART_JY61P/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


