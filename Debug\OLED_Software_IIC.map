******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Thu Jul 18 14:59:44 2024

OUTPUT FILE NAME:   <OLED_Software_IIC.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002e81


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00004d68  0001b298  R  X
  SRAM                  20200000   00008000  00000754  000078ac  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000031c8   000031c8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00003108   00003108    r-x .text
000031d0    000031d0    00001ba0   00001ba0    r--
  000031d0    000031d0    00001b50   00001b50    r-- .rodata
  00004d20    00004d20    00000050   00000050    r-- .cinit
20200000    20200000    00000554   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .bss
  20200400    20200400    00000154   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00003108     
                  000000c0    00000a00     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000ac0    00000334     OLED.o (.text.OLED_ShowImageArea)
                  00000df4    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001014    000001f0     OLED.o (.text.OLED_ShowChineseArea)
                  00001204    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  000013e0    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00001572    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001574    00000148     OLED.o (.text.OLED_ReverseArea)
                  000016bc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000017f8    00000124     OLED.o (.text.OLED_ShowCharArea)
                  0000191c    00000120     OLED.o (.text.OLED_ShowMixStringArea)
                  00001a3c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00001b5c    0000011c     OLED.o (.text.OLED_DrawRectangle)
                  00001c78    00000118     Menu.o (.text.ShowMenuList)
                  00001d90    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00001e9c    000000f4     Menu.o (.text.DrawFrame)
                  00001f90    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00002074    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00002150    000000dc     OLED.o (.text.OLED_ClearArea)
                  0000222c    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00002304    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000023a6    0000009a     OLED.o (.text.OLED_Init)
                  00002440    00000086     OLED.o (.text.OLED_PrintfMixArea)
                  000024c6    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000024c8    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00002540    00000070     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000025b0    0000006c                            : comparedf2.c.obj (.text.__ledf2)
                  0000261c    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00002682    00000002     --HOLE-- [fill = 0]
                  00002684    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000026e6    00000002     --HOLE-- [fill = 0]
                  000026e8    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00002744    00000058     OLED.o (.text.OLED_Clear)
                  0000279c    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  000027f4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000284c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000028a2    00000054     OLED.o (.text.OLED_I2C_SendByte)
                  000028f6    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00002948    00000050     OLED.o (.text.OLED_WriteData)
                  00002998    00000050     main.o (.text.main)
                  000029e8    0000004c     OLED.o (.text.OLED_DrawPoint)
                  00002a34    0000004a     OLED.o (.text.OLED_GPIO_Init)
                  00002a7e    00000002     --HOLE-- [fill = 0]
                  00002a80    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00002aca    00000002     --HOLE-- [fill = 0]
                  00002acc    00000048     OLED.o (.text.OLED_Update)
                  00002b14    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00002b58    00000040     OLED.o (.text.OLED_W_SCL)
                  00002b98    00000040     OLED.o (.text.OLED_W_SDA)
                  00002bd8    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00002c18    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00002c58    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002c94    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00002cd0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00002d0a    00000036     OLED.o (.text.OLED_SetCursor)
                  00002d40    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00002d74    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00002da4    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00002dd4    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00002e00    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  00002e2c    0000002a     OLED.o (.text.OLED_WriteCommand)
                  00002e56    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002e7e    00000002     --HOLE-- [fill = 0]
                  00002e80    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002ea8    00000024     OLED.o (.text.OLED_I2C_Start)
                  00002ecc    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  00002ef0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00002f12    00000002     --HOLE-- [fill = 0]
                  00002f14    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00002f34    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  00002f52    00000002     --HOLE-- [fill = 0]
                  00002f54    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002f70    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00002f8c    0000001c     OLED.o (.text.OLED_I2C_Stop)
                  00002fa8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002fc0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002fd8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002ff0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00003008    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00003020    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00003038    00000018     libc.a : vsprintf.c.obj (.text._outs)
                  00003050    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00003064    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00003078    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000308c    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000030a0    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000030b4    00000014            : strcmp.c.obj (.text.strcmp)
                  000030c8    00000012            : memcpy16.S.obj (.text:TI_memcpy_small)
                  000030da    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  000030ec    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  000030fc    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  0000310c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  0000311c    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  0000312c    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  0000313a    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00003148    0000000e            : strlen.c.obj (.text.strlen)
                  00003156    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00003164    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00003170    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000317a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00003184    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00003194    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000319e    0000000a            : vsprintf.c.obj (.text._outc)
                  000031a8    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  000031b0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000031b8    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000031bc    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000031c0    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000031c4    00000004            : exit.c.obj (.text:abort)

.cinit     0    00004d20    00000050     
                  00004d20    0000002a     (.cinit..data.load) [load image, compression = lzss]
                  00004d4a    00000002     --HOLE-- [fill = 0]
                  00004d4c    0000000c     (__TI_handler_table)
                  00004d58    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00004d60    00000010     (__TI_cinit_table)

.rodata    0    000031d0    00001b50     
                  000031d0    000007c4     OLED_DATA.o (.rodata.OLED_CF12x12)
                  00003994    000005f0     OLED_DATA.o (.rodata.OLED_F8x16)
                  00003f84    00000532     OLED_DATA.o (.rodata.OLED_F7x12)
                  000044b6    000004a4     OLED_DATA.o (.rodata.OLED_CF16x16)
                  0000495a    0000023a     OLED_DATA.o (.rodata.OLED_F6x8)
                  00004b94    00000008     main.o (.rodata.str1.42969106790848045441)
                  00004b9c    00000001     OLED.o (.rodata.str1.101278950932238447941)
                  00004b9d    00000003     --HOLE-- [fill = 0]
                  00004ba0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00004ca1    00000003     --HOLE-- [fill = 0]
                  00004ca4    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  00004ccc    00000011     libc.a : _printfi.c.obj (.rodata.str1.11645776875810915891)
                  00004cdd    00000011     main.o (.rodata.str1.176328792963337784071)
                  00004cee    00000011     libc.a : _printfi.c.obj (.rodata.str1.44690500295887128011)
                  00004cff    00000011     main.o (.rodata.str1.97993385775340092391)
                  00004d10    0000000e     main.o (.rodata.str1.52501554851255701521)
                  00004d1e    00000002     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000400     UNINITIALIZED
                  20200000    00000400     (.common:OLED_DisplayBuf)

.data      0    20200400    00000154     UNINITIALIZED
                  20200400    00000140     main.o (.data.MainMenu)
                  20200540    00000010     main.o (.data.ShowMainMenu)
                  20200550    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             480     40        0      
       main.o                         80      56        336    
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         568     288       336    
                                                               
    .\OLED\
       OLED_DATA.o                    0       6596      0      
       OLED.o                         3822    1         1024   
       Menu.o                         524     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4346    6597      1024   
                                                               
    E:/ti/sdk/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   288     0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         298     0         0      
                                                               
    E:\software\develop\CCS\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4558    34        0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     120     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       memccpy.c.obj                  34      0         0      
       strcmp.c.obj                   20      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       strlen.c.obj                   14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         5482    291       4      
                                                               
    E:\software\develop\CCS\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   418     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   228     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       muldsi3.S.obj                  58      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1844    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       78        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   12538   7254      1876   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00004d60 records: 2, size/record: 8, table size: 16
	.data: load addr=00004d20, load size=0000002a bytes, run addr=20200400, run size=00000154 bytes, compression=lzss
	.bss: load addr=00004d58, load size=00000008 bytes, run addr=20200000, run size=00000400 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00004d4c records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000013e1     00003184     00003182   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)

[1 trampolines]
[1 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
000031b9  ADC0_IRQHandler                      
000031b9  ADC1_IRQHandler                      
000031b9  AES_IRQHandler                       
000031c4  C$$EXIT                              
000031b9  CANFD0_IRQHandler                    
000031b9  DAC0_IRQHandler                      
00003171  DL_Common_delayCycles                
00002075  DL_SYSCTL_configSYSPLL               
00002b15  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
000031b9  DMA_IRQHandler                       
000031b9  Default_Handler                      
00001e9d  DrawFrame                            
000031b9  GROUP0_IRQHandler                    
000031b9  GROUP1_IRQHandler                    
000031b9  HardFault_Handler                    
000031b9  I2C0_IRQHandler                      
000031b9  I2C1_IRQHandler                      
20200400  MainMenu                             
000031b9  NMI_Handler                          
000031d0  OLED_CF12x12                         
000044b6  OLED_CF16x16                         
00002745  OLED_Clear                           
00002151  OLED_ClearArea                       
20200000  OLED_DisplayBuf                      
000029e9  OLED_DrawPoint                       
00001b5d  OLED_DrawRectangle                   
0000495a  OLED_F6x8                            
00003f84  OLED_F7x12                           
00003994  OLED_F8x16                           
00002a35  OLED_GPIO_Init                       
000028a3  OLED_I2C_SendByte                    
00002ea9  OLED_I2C_Start                       
00002f8d  OLED_I2C_Stop                        
000023a7  OLED_Init                            
00002441  OLED_PrintfMixArea                   
00001575  OLED_ReverseArea                     
00002d0b  OLED_SetCursor                       
000017f9  OLED_ShowCharArea                    
00001015  OLED_ShowChineseArea                 
00000ac1  OLED_ShowImageArea                   
0000191d  OLED_ShowMixStringArea               
00002acd  OLED_Update                          
00002b59  OLED_W_SCL                           
00002b99  OLED_W_SDA                           
00002e2d  OLED_WriteCommand                    
00002949  OLED_WriteData                       
000031b9  PendSV_Handler                       
000031b9  RTC_IRQHandler                       
000031bd  Reset_Handler                        
000031b9  SPI0_IRQHandler                      
000031b9  SPI1_IRQHandler                      
000031b9  SVC_Handler                          
00002d75  SYSCFG_DL_GPIO_init                  
00002c59  SYSCFG_DL_SYSCTL_init                
000030fd  SYSCFG_DL_init                       
00002d41  SYSCFG_DL_initPower                  
20200540  ShowMainMenu                         
00001c79  ShowMenuList                         
000031b9  SysTick_Handler                      
000031b9  TIMA0_IRQHandler                     
000031b9  TIMA1_IRQHandler                     
000031b9  TIMG0_IRQHandler                     
000031b9  TIMG12_IRQHandler                    
000031b9  TIMG6_IRQHandler                     
000031b9  TIMG7_IRQHandler                     
000031b9  TIMG8_IRQHandler                     
000030c9  TI_memcpy_small                      
00003157  TI_memset_small                      
000031b9  UART0_IRQHandler                     
000031b9  UART1_IRQHandler                     
000031b9  UART2_IRQHandler                     
000031b9  UART3_IRQHandler                     
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00004d60  __TI_CINIT_Base                      
00004d70  __TI_CINIT_Limit                     
00004d70  __TI_CINIT_Warm                      
00004d4c  __TI_Handler_Table_Base              
00004d58  __TI_Handler_Table_Limit             
00002c95  __TI_auto_init_nobinit_nopinit       
000024c9  __TI_decompress_lzss                 
000030db  __TI_decompress_none                 
0000279d  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
0000311d  __TI_zero_init                       
000013eb  __adddf3                             
00004ba0  __aeabi_ctype_table_                 
00004ba0  __aeabi_ctype_table_C                
00002a81  __aeabi_d2iz                         
000013eb  __aeabi_dadd                         
00002685  __aeabi_dcmpeq                       
000026c1  __aeabi_dcmpge                       
000026d5  __aeabi_dcmpgt                       
000026ad  __aeabi_dcmple                       
00002699  __aeabi_dcmplt                       
00001d91  __aeabi_ddiv                         
00001f91  __aeabi_dmul                         
000013e1  __aeabi_dsub                         
20200550  __aeabi_errno                        
000031a9  __aeabi_errno_addr                   
00002dd5  __aeabi_i2d                          
0000284d  __aeabi_idiv                         
00001573  __aeabi_idiv0                        
0000284d  __aeabi_idivmod                      
000024c7  __aeabi_ldiv0                        
00002f35  __aeabi_llsl                         
00002ecd  __aeabi_lmul                         
00003165  __aeabi_memclr                       
00003165  __aeabi_memclr4                      
00003165  __aeabi_memclr8                      
000031b1  __aeabi_memcpy                       
000031b1  __aeabi_memcpy4                      
000031b1  __aeabi_memcpy8                      
0000312d  __aeabi_memset                       
0000312d  __aeabi_memset4                      
0000312d  __aeabi_memset8                      
00002bd9  __aeabi_uidiv                        
00002bd9  __aeabi_uidivmod                     
0000308d  __aeabi_uldivmod                     
00002f35  __ashldi3                            
ffffffff  __binit__                            
000025b1  __cmpdf2                             
00001d91  __divdf3                             
000025b1  __eqdf2                              
00002a81  __fixdfsi                            
00002dd5  __floatsidf                          
00002541  __gedf2                              
00002541  __gtdf2                              
000025b1  __ledf2                              
000025b1  __ltdf2                              
UNDEFED   __mpu_init                           
00001f91  __muldf3                             
00002ecd  __muldi3                             
00002cd1  __muldsi3                            
000025b1  __nedf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000013e1  __subdf3                             
00002305  __udivmoddi4                         
00002e81  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
000031c1  _system_pre_init                     
000031c5  abort                                
00002c19  atoi                                 
ffffffff  binit                                
000026e9  frexp                                
000026e9  frexpl                               
00000000  interruptVectors                     
0000222d  ldexp                                
0000222d  ldexpl                               
00002999  main                                 
00002ef1  memccpy                              
0000222d  scalbn                               
0000222d  scalbnl                              
000030b5  strcmp                               
00003149  strlen                               
00002e01  vsprintf                             
0000310d  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000ac1  OLED_ShowImageArea                   
00001015  OLED_ShowChineseArea                 
000013e1  __aeabi_dsub                         
000013e1  __subdf3                             
000013eb  __adddf3                             
000013eb  __aeabi_dadd                         
00001573  __aeabi_idiv0                        
00001575  OLED_ReverseArea                     
000017f9  OLED_ShowCharArea                    
0000191d  OLED_ShowMixStringArea               
00001b5d  OLED_DrawRectangle                   
00001c79  ShowMenuList                         
00001d91  __aeabi_ddiv                         
00001d91  __divdf3                             
00001e9d  DrawFrame                            
00001f91  __aeabi_dmul                         
00001f91  __muldf3                             
00002075  DL_SYSCTL_configSYSPLL               
00002151  OLED_ClearArea                       
0000222d  ldexp                                
0000222d  ldexpl                               
0000222d  scalbn                               
0000222d  scalbnl                              
00002305  __udivmoddi4                         
000023a7  OLED_Init                            
00002441  OLED_PrintfMixArea                   
000024c7  __aeabi_ldiv0                        
000024c9  __TI_decompress_lzss                 
00002541  __gedf2                              
00002541  __gtdf2                              
000025b1  __cmpdf2                             
000025b1  __eqdf2                              
000025b1  __ledf2                              
000025b1  __ltdf2                              
000025b1  __nedf2                              
00002685  __aeabi_dcmpeq                       
00002699  __aeabi_dcmplt                       
000026ad  __aeabi_dcmple                       
000026c1  __aeabi_dcmpge                       
000026d5  __aeabi_dcmpgt                       
000026e9  frexp                                
000026e9  frexpl                               
00002745  OLED_Clear                           
0000279d  __TI_ltoa                            
0000284d  __aeabi_idiv                         
0000284d  __aeabi_idivmod                      
000028a3  OLED_I2C_SendByte                    
00002949  OLED_WriteData                       
00002999  main                                 
000029e9  OLED_DrawPoint                       
00002a35  OLED_GPIO_Init                       
00002a81  __aeabi_d2iz                         
00002a81  __fixdfsi                            
00002acd  OLED_Update                          
00002b15  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00002b59  OLED_W_SCL                           
00002b99  OLED_W_SDA                           
00002bd9  __aeabi_uidiv                        
00002bd9  __aeabi_uidivmod                     
00002c19  atoi                                 
00002c59  SYSCFG_DL_SYSCTL_init                
00002c95  __TI_auto_init_nobinit_nopinit       
00002cd1  __muldsi3                            
00002d0b  OLED_SetCursor                       
00002d41  SYSCFG_DL_initPower                  
00002d75  SYSCFG_DL_GPIO_init                  
00002dd5  __aeabi_i2d                          
00002dd5  __floatsidf                          
00002e01  vsprintf                             
00002e2d  OLED_WriteCommand                    
00002e81  _c_int00_noargs                      
00002ea9  OLED_I2C_Start                       
00002ecd  __aeabi_lmul                         
00002ecd  __muldi3                             
00002ef1  memccpy                              
00002f35  __aeabi_llsl                         
00002f35  __ashldi3                            
00002f8d  OLED_I2C_Stop                        
0000308d  __aeabi_uldivmod                     
000030b5  strcmp                               
000030c9  TI_memcpy_small                      
000030db  __TI_decompress_none                 
000030fd  SYSCFG_DL_init                       
0000310d  wcslen                               
0000311d  __TI_zero_init                       
0000312d  __aeabi_memset                       
0000312d  __aeabi_memset4                      
0000312d  __aeabi_memset8                      
00003149  strlen                               
00003157  TI_memset_small                      
00003165  __aeabi_memclr                       
00003165  __aeabi_memclr4                      
00003165  __aeabi_memclr8                      
00003171  DL_Common_delayCycles                
000031a9  __aeabi_errno_addr                   
000031b1  __aeabi_memcpy                       
000031b1  __aeabi_memcpy4                      
000031b1  __aeabi_memcpy8                      
000031b9  ADC0_IRQHandler                      
000031b9  ADC1_IRQHandler                      
000031b9  AES_IRQHandler                       
000031b9  CANFD0_IRQHandler                    
000031b9  DAC0_IRQHandler                      
000031b9  DMA_IRQHandler                       
000031b9  Default_Handler                      
000031b9  GROUP0_IRQHandler                    
000031b9  GROUP1_IRQHandler                    
000031b9  HardFault_Handler                    
000031b9  I2C0_IRQHandler                      
000031b9  I2C1_IRQHandler                      
000031b9  NMI_Handler                          
000031b9  PendSV_Handler                       
000031b9  RTC_IRQHandler                       
000031b9  SPI0_IRQHandler                      
000031b9  SPI1_IRQHandler                      
000031b9  SVC_Handler                          
000031b9  SysTick_Handler                      
000031b9  TIMA0_IRQHandler                     
000031b9  TIMA1_IRQHandler                     
000031b9  TIMG0_IRQHandler                     
000031b9  TIMG12_IRQHandler                    
000031b9  TIMG6_IRQHandler                     
000031b9  TIMG7_IRQHandler                     
000031b9  TIMG8_IRQHandler                     
000031b9  UART0_IRQHandler                     
000031b9  UART1_IRQHandler                     
000031b9  UART2_IRQHandler                     
000031b9  UART3_IRQHandler                     
000031bd  Reset_Handler                        
000031c1  _system_pre_init                     
000031c4  C$$EXIT                              
000031c5  abort                                
000031d0  OLED_CF12x12                         
00003994  OLED_F8x16                           
00003f84  OLED_F7x12                           
000044b6  OLED_CF16x16                         
0000495a  OLED_F6x8                            
00004ba0  __aeabi_ctype_table_                 
00004ba0  __aeabi_ctype_table_C                
00004d4c  __TI_Handler_Table_Base              
00004d58  __TI_Handler_Table_Limit             
00004d60  __TI_CINIT_Base                      
00004d70  __TI_CINIT_Limit                     
00004d70  __TI_CINIT_Warm                      
20200000  OLED_DisplayBuf                      
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
20200400  MainMenu                             
20200540  ShowMainMenu                         
20200550  __aeabi_errno                        
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[174 symbols]
