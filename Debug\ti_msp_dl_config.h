/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)



#define CPUCLK_FREQ                                                     80000000



/* Defines for PWMA */
#define PWMA_INST                                                          TIMA0
#define PWMA_INST_IRQHandler                                    TIMA0_IRQHandler
#define PWMA_INST_INT_IRQN                                      (TIMA0_INT_IRQn)
#define PWMA_INST_CLK_FREQ                                               2000000
/* GPIO defines for channel 0 */
#define GPIO_PWMA_C0_PORT                                                  GPIOB
#define GPIO_PWMA_C0_PIN                                          DL_GPIO_PIN_14
#define GPIO_PWMA_C0_IOMUX                                       (IOMUX_PINCM31)
#define GPIO_PWMA_C0_IOMUX_FUNC                      IOMUX_PINCM31_PF_TIMA0_CCP0
#define GPIO_PWMA_C0_IDX                                     DL_TIMER_CC_0_INDEX
/* GPIO defines for channel 1 */
#define GPIO_PWMA_C1_PORT                                                  GPIOA
#define GPIO_PWMA_C1_PIN                                           DL_GPIO_PIN_7
#define GPIO_PWMA_C1_IOMUX                                       (IOMUX_PINCM14)
#define GPIO_PWMA_C1_IOMUX_FUNC                      IOMUX_PINCM14_PF_TIMA0_CCP1
#define GPIO_PWMA_C1_IDX                                     DL_TIMER_CC_1_INDEX



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMG0)
#define TIMER_0_INST_IRQHandler                                 TIMG0_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMG0_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                           (499U)
/* Defines for TIMER_control */
#define TIMER_control_INST                                               (TIMG6)
#define TIMER_control_INST_IRQHandler                           TIMG6_IRQHandler
#define TIMER_control_INST_INT_IRQN                             (TIMG6_INT_IRQn)
#define TIMER_control_INST_LOAD_VALUE                                      (99U)



/* Defines for UART_JY61P */
#define UART_JY61P_INST                                                    UART2
#define UART_JY61P_INST_IRQHandler                              UART2_IRQHandler
#define UART_JY61P_INST_INT_IRQN                                  UART2_INT_IRQn
#define GPIO_UART_JY61P_RX_PORT                                            GPIOB
#define GPIO_UART_JY61P_TX_PORT                                            GPIOB
#define GPIO_UART_JY61P_RX_PIN                                    DL_GPIO_PIN_16
#define GPIO_UART_JY61P_TX_PIN                                    DL_GPIO_PIN_17
#define GPIO_UART_JY61P_IOMUX_RX                                 (IOMUX_PINCM33)
#define GPIO_UART_JY61P_IOMUX_TX                                 (IOMUX_PINCM43)
#define GPIO_UART_JY61P_IOMUX_RX_FUNC                  IOMUX_PINCM33_PF_UART2_RX
#define GPIO_UART_JY61P_IOMUX_TX_FUNC                  IOMUX_PINCM43_PF_UART2_TX
#define UART_JY61P_BAUD_RATE                                              (9600)
#define UART_JY61P_IBRD_40_MHZ_9600_BAUD                                   (260)
#define UART_JY61P_FBRD_40_MHZ_9600_BAUD                                    (27)
/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                (115200)
#define UART_0_IBRD_40_MHZ_115200_BAUD                                      (21)
#define UART_0_FBRD_40_MHZ_115200_BAUD                                      (45)





/* Defines for ADC1 */
#define ADC1_INST                                                           ADC1
#define ADC1_INST_IRQHandler                                     ADC1_IRQHandler
#define ADC1_INST_INT_IRQN                                       (ADC1_INT_IRQn)
#define ADC1_ADCMEM_ADC_Channel0                              DL_ADC12_MEM_IDX_0
#define ADC1_ADCMEM_ADC_Channel0_REF             DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC1_ADCMEM_ADC_Channel0_REF_VOLTAGE_V                                     3.3
#define GPIO_ADC1_C0_PORT                                                  GPIOA
#define GPIO_ADC1_C0_PIN                                          DL_GPIO_PIN_15



/* Port definition for Pin Group GPIO_OLED */
#define GPIO_OLED_PORT                                                   (GPIOA)

/* Defines for PIN_SCL: GPIOA.1 with pinCMx 2 on package pin 34 */
#define GPIO_OLED_PIN_SCL_PIN                                    (DL_GPIO_PIN_1)
#define GPIO_OLED_PIN_SCL_IOMUX                                   (IOMUX_PINCM2)
/* Defines for PIN_SDA: GPIOA.0 with pinCMx 1 on package pin 33 */
#define GPIO_OLED_PIN_SDA_PIN                                    (DL_GPIO_PIN_0)
#define GPIO_OLED_PIN_SDA_IOMUX                                   (IOMUX_PINCM1)
/* Defines for K1: GPIOB.19 with pinCMx 45 on package pin 16 */
#define GPIO_Key_K1_PORT                                                 (GPIOB)
#define GPIO_Key_K1_PIN                                         (DL_GPIO_PIN_19)
#define GPIO_Key_K1_IOMUX                                        (IOMUX_PINCM45)
/* Defines for K2: GPIOA.16 with pinCMx 38 on package pin 9 */
#define GPIO_Key_K2_PORT                                                 (GPIOA)
#define GPIO_Key_K2_PIN                                         (DL_GPIO_PIN_16)
#define GPIO_Key_K2_IOMUX                                        (IOMUX_PINCM38)
/* Port definition for Pin Group TB6612 */
#define TB6612_PORT                                                      (GPIOB)

/* Defines for AIN1: GPIOB.9 with pinCMx 26 on package pin 61 */
#define TB6612_AIN1_PIN                                          (DL_GPIO_PIN_9)
#define TB6612_AIN1_IOMUX                                        (IOMUX_PINCM26)
/* Defines for AIN2: GPIOB.10 with pinCMx 27 on package pin 62 */
#define TB6612_AIN2_PIN                                         (DL_GPIO_PIN_10)
#define TB6612_AIN2_IOMUX                                        (IOMUX_PINCM27)
/* Defines for BIN1: GPIOB.7 with pinCMx 24 on package pin 59 */
#define TB6612_BIN1_PIN                                          (DL_GPIO_PIN_7)
#define TB6612_BIN1_IOMUX                                        (IOMUX_PINCM24)
/* Defines for BIN2: GPIOB.6 with pinCMx 23 on package pin 58 */
#define TB6612_BIN2_PIN                                          (DL_GPIO_PIN_6)
#define TB6612_BIN2_IOMUX                                        (IOMUX_PINCM23)
/* Port definition for Pin Group Gray_Address */
#define Gray_Address_PORT                                                (GPIOB)

/* Defines for PIN_0: GPIOB.0 with pinCMx 12 on package pin 47 */
#define Gray_Address_PIN_0_PIN                                   (DL_GPIO_PIN_0)
#define Gray_Address_PIN_0_IOMUX                                 (IOMUX_PINCM12)
/* Defines for PIN_1: GPIOB.1 with pinCMx 13 on package pin 48 */
#define Gray_Address_PIN_1_PIN                                   (DL_GPIO_PIN_1)
#define Gray_Address_PIN_1_IOMUX                                 (IOMUX_PINCM13)
/* Defines for PIN_2: GPIOB.2 with pinCMx 15 on package pin 50 */
#define Gray_Address_PIN_2_PIN                                   (DL_GPIO_PIN_2)
#define Gray_Address_PIN_2_IOMUX                                 (IOMUX_PINCM15)

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWMA_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_TIMER_control_init(void);
void SYSCFG_DL_UART_JY61P_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_ADC1_init(void);


bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */
